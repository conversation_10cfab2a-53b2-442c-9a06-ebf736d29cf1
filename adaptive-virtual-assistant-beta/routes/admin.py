# routes/admin.py

from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from data.data import load_profiles, save_profiles
import logging

admin = Blueprint('admin', __name__, url_prefix='/admin')


# --- Function: admin_required ---
def admin_required():
    """Helper to check admin access."""
    if not current_user.is_authenticated or not current_user.is_admin:
        flash('Access denied: Admins only.', 'danger')
        return False
    return True

@admin.route('/dashboard')
@login_required

# --- Function: admin_dashboard ---
def admin_dashboard():
    """Render the admin dashboard to view all users."""
    if not admin_required():
        return redirect(url_for('main.index'))

    # Begin try block
    try:
        profiles = load_profiles()
        return render_template('admin_dashboard.html', users=profiles, current_user=current_user)
    # Handle exception
    except Exception as e:
        logging.error(f"Failed to load admin dashboard: {e}")
        flash('Error loading admin dashboard.', 'danger')
        return redirect(url_for('main.index'))

@admin.route('/user/<username>')
@login_required

# --- Function: view_user ---
def view_user(username):
    """View a specific user's profile and history."""
    if not admin_required():
        return redirect(url_for('main.index'))

    # Begin try block
    try:
        profiles = load_profiles()
        if username not in profiles:
            flash('User not found.', 'warning')
            return redirect(url_for('admin.admin_dashboard'))
            
        user_profile = profiles[username]
        
        # Ensure required fields exist to prevent template errors
        if 'answer_type_weights' not in user_profile:
            user_profile['answer_type_weights'] = {
                "informational": 0.5,
                "real_world": 0.5,
                "cause_and_effect": 0.5,
                "goal_based": 0.5
            }
            
        if 'weight_adjustment_count' not in user_profile:
            user_profile['weight_adjustment_count'] = 0
            
        return render_template('view_user.html', profile=user_profile)
    # Handle exception
    except Exception as e:
        logging.error(f"Error viewing user profile: {e}")
        flash('Error loading user profile.', 'danger')
        return redirect(url_for('admin.admin_dashboard'))

@admin.route('/user/<username>/edit', methods=['GET', 'POST'])
@login_required

# --- Function: edit_user ---
def edit_user(username):
    """Edit a specific user’s profile."""
    if not admin_required():
        return redirect(url_for('main.index'))

    profiles = load_profiles()
    user_profile = profiles.get(username)

    if not user_profile:
        flash('User not found.', 'danger')
        return redirect(url_for('admin.admin_dashboard'))

    if request.method == 'POST':
        # Begin try block
        try:
            # Basic fields (string values)
            string_fields = [
                'name', 'email', 'phone_number', 'age', 'gender',
                'academic_level', 'preferred_subject', 'learning_style',
                'location', 'language', 'timezone', 'cultural_background'
            ]
            for field in string_fields:
                user_profile[field] = request.form.get(field, user_profile.get(field, ''))

            # List-based fields (split comma-separated)
            list_fields = ['hobbies', 'goals', 'strengths', 'weaknesses']
            for field in list_fields:
                value = request.form.get(field, '')
                user_profile[field] = [item.strip() for item in value.split(',') if item.strip()]

            # Boolean admin field
            user_profile['is_admin'] = 'is_admin' in request.form

            save_profiles(profiles)
            flash('User profile updated successfully.', 'success')
            return redirect(url_for('admin.view_user', username=username))

        # Handle exception
        except Exception as e:
            logging.error(f"Error updating profile for {username}: {e}")
            flash('Error saving user profile.', 'danger')
            return redirect(url_for('admin.edit_user', username=username))

    return render_template('edit_user.html', profile=user_profile)