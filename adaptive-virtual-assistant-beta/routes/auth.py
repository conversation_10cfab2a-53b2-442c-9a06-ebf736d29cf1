# routes/auth.py

from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_user, logout_user, login_required, current_user
import logging
from models.user import User
from data.data import load_profiles, save_profiles

auth = Blueprint('auth', __name__, url_prefix='/auth')

@auth.route('/login', methods=['GET', 'POST'])

# --- Function: login ---
def login():
    """Handle user login."""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        if not username or not password:
            flash('Please enter both username and password.', 'danger')
            return render_template('login.html')
        profiles = load_profiles()
        if username in profiles:
            user_profile = profiles[username]
            stored_password_hash = user_profile.get('password_hash')
            logging.debug(f"User {username} found. Stored hash: {stored_password_hash}")
            if stored_password_hash:
                user = User(username, is_admin=user_profile.get('is_admin', False))
                user.password_hash = stored_password_hash
                if user.verify_password(password):
                    login_user(user)
                    flash('Logged in successfully.', 'success')
                    logging.info(f"User '{username}' logged in.")
                    return redirect(url_for('main.index'))
                else:
                    logging.warning(f"Password verification failed for user '{username}'.")
        else:
            logging.warning(f"User '{username}' not found in profiles.")
        flash('Invalid username or password.', 'danger')
    return render_template('login.html')


@auth.route('/logout')
@login_required

# --- Function: logout ---
def logout():
    """Handle user logout."""
    username = current_user.get_id()
    logout_user()
    flash('You have been logged out.', 'info')
    logging.info(f"User '{username}' logged out.")
    return redirect(url_for('auth.login'))

@auth.route('/register', methods=['GET', 'POST'])

# --- Function: register ---
def register():
    """Handle user registration."""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        if not username or not password or not confirm_password:
            flash('Please fill out all fields.', 'danger')
            return render_template('register.html')
        if password != confirm_password:
            flash('Passwords do not match.', 'danger')
            return render_template('register.html')
        profiles = load_profiles()
        if username in profiles:
            flash('Username already exists.', 'danger')
            return render_template('register.html')
        password_hash = User.generate_password_hash_static(password)
        new_user = {
            "user_id": len(profiles) + 1,
            "username": username,
            "password_hash": password_hash,
            "name": "",
            "email": "",
            "phone_number": "",
            "age": 0,
            "gender": "",
            "academic_level": "",
            "preferred_subject": "",
            "learning_style": "default",
            "location": "",
            "language": "",
            "timezone": "",
            "cultural_background": "",
            "hobbies": [],
            "goals": [],
            "strengths": [],
            "weaknesses": [],
            "is_admin": False,
            "histories": []
        }
        profiles[username] = new_user
        save_profiles(profiles)
        flash('Registration successful. Please log in.', 'success')
        logging.info(f"New user registered: '{username}'.")
        return redirect(url_for('auth.login'))
    return render_template('register.html')
