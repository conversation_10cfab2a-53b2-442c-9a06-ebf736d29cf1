# routes/main.py

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from datetime import datetime
import logging
import random
import markdown
import re

from data.data import load_profiles, save_profiles, update_profile
from services.answer_service import get_ai_answer, assess_understanding, client
from services.similarity_service import calculate_similarity_score, adjust_answer_weights

augment_weight_from_answer = False

main = Blueprint('main', __name__, url_prefix='/')


# --- Function: detect_answer_style ---
def detect_answer_style(answer_text):
    """Extract the answer style from the generated answer text."""
    answer_text = answer_text.lower()
    
    patterns = {
        "real_world": ["real world style", "real-world style"],
        "informational": ["informational response", "informational answer", "informational style"],
        "cause_and_effect": ["cause and effect", "cause-and-effect style"],
        "goal_based": ["goal based", "goal-based style"]
    }
    
    for style, phrases in patterns.items():
        for phrase in phrases:
            if phrase in answer_text:
                return style
                
    simple_patterns = {
        "real_world": ["real world", "real-world"],
        "informational": ["informational", "information"],
        "cause_and_effect": ["cause", "effect"],
        "goal_based": ["goal"]
    }
    
    for style, phrases in simple_patterns.items():
        for phrase in phrases:
            if phrase in answer_text[:500]:  
                return style
    
    return "informational"  # Default fallback

@main.route('/')
@login_required

# --- Function: index ---
def index():
    """Render the home page with predefined questions."""
    predefined_questions = [
        "What is math?",
        "How does AI work?",
        "Explain the theory of relativity.",
        "What is machine learning?",
        "How to solve quadratic equations?",
        "What are the benefits of exercise?",
        "Tell me about quantum computing.",
        "What is the capital of France?",
        "How to improve my programming skills?",
        "What is the best way to learn a new language?"
    ]
    return render_template('index.html', 
                           predefined_questions=predefined_questions,
                           username=current_user.username)

@main.route('/get_answer', methods=['POST'])
@login_required

# --- Function: get_answer ---
def get_answer():
    """Generate an answer exclusively using the Gemini API and return it as rendered HTML."""
    data = request.get_json()
    if not data:
        logging.warning("Invalid request data received.")
        return jsonify({"error": "Invalid request data"}), 400

    question = data.get('question', '').strip()
    username = current_user.get_id()
    if not question:
        logging.warning(f"Empty question received from user '{username}'.")
        return jsonify({"error": "Question cannot be empty."}), 400
    if not isinstance(question, str) or len(question) > 500:
        logging.warning(f"Invalid question format or too long from user '{username}'.")
        return jsonify({"error": "Invalid question."}), 400

    # Load user profile
    profiles = load_profiles()
    user_profile = profiles.get(username)
    if not user_profile:
        logging.warning(f"User profile not found for user '{username}'.")
        return jsonify({"error": "User profile not found."}), 404

    # Use Gemini API to generate a custom answer
    answer = get_ai_answer(
        question,
        user_profile,
        current_app.config.get('KEYWORD_SYNONYMS', {})
    )
    if not answer:
        logging.error(f"Gemini API failed to generate an answer for user '{username}' and question: {question}")
        return jsonify({"error": "Could not generate an answer. Please try again later."}), 500

    # Convert the Markdown answer to HTML (using extensions for better formatting)
    answer_html = markdown.markdown(answer, extensions=['extra', 'nl2br'])

    # Detect the answer style
    detected_answer_style = detect_answer_style(answer)

    # Save to user's history
    history_entry = {
        'question': question,
        'answer': answer_html,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'answer_style': detected_answer_style
    }
    histories = user_profile.get('histories', [])
    histories.append(history_entry)
    user_profile['histories'] = histories
    update_profile(username, {'histories': histories})

    logging.info(f"Returning AI-generated answer for user '{username}': {answer_html}")

    # Log change in weight
    weight_fields = [
        'informational', 'real_world', 'cause_and_effect', 'goal_based'
    ]
    weight_words = [
        'informational', 'real world', 'cause and effect', 'goal based'
    ]

    print(f'Augment profile question-type based on answer: {augment_weight_from_answer}')

    if augment_weight_from_answer:
        # Perhaps add try catch here
        user_profile = profiles.get(current_user.get_id())

        # Debug
        # print(answer_html)
        i = 0
        for weight_field in weight_fields:
            # print(weight_field)
            if weight_words[i] in answer_html.lower():
                # print(f'{weight_words[i]} is in answer_html')
                # logging.info(f"{weight_words[i]} found in answer_html string, recommend increasing.")
                # print(f'user_profile["answer_type_weights"][weight_field]: {user_profile["answer_type_weights"][weight_field]}')
                if user_profile["answer_type_weights"][weight_field] <= 0.9:
                    # print(f'user_profile["answer_type_weights"][weight_field] <= 0.9')
                    # logging.info(f"Profile's answer_type_weights[{weight_field}] value previously {user_profile['answer_type_weights'][weight_field]}")
                    user_profile["answer_type_weights"][weight_field] += 0.1
                    # logging.info(f"Profile's answer_type_weights[{weight_field}] value updated to {user_profile['answer_type_weights'][weight_field]}")
            i+=1

        save_profiles(profiles)
    return jsonify({
        "answer": answer_html,
        "answer_style": detected_answer_style 
    })


@main.route('/about')

# --- Function: about ---
def about():
    """Render the about page."""
    return render_template('about.html')

@main.route('/history')
@login_required

# --- Function: history_view ---
def history_view():
    """Display the user's question history with search and pagination."""
    # Begin try block
    try:
        profiles = load_profiles()
        user_profile = profiles.get(current_user.get_id())
        
        if not user_profile:
            flash('User profile not found.', 'danger')
            return redirect(url_for('main.index'))

        history = user_profile.get('histories', [])
        search_query = request.args.get('q', '').strip().lower()
        
        if search_query:
            history = [
                entry for entry in history 
                if search_query in entry['question'].lower() or 
                   search_query in entry['answer'].lower()
            ]
            logging.info(f"Search '{search_query}' returned {len(history)} results.")

        # Pagination
        page = request.args.get('page', 1, type=int)
        per_page = 10
        total = len(history)
        pages = (total + per_page - 1) // per_page
        start = (page - 1) * per_page
        paginated_history = history[start:start + per_page]
        
        return render_template('history.html', 
                             history=paginated_history,
                             page=page,
                             pages=pages,
                             q=search_query,
                             username=current_user.username)

    # Handle exception
    except Exception as e:
        logging.error(f"Error in history_view: {str(e)}")
        flash('An error occurred while loading history.', 'danger')
        return redirect(url_for('main.index'))

@main.route('/profile')
@login_required

# --- Function: profile_view ---
def profile_view():
    """Render the user's profile page."""
    # Begin try block
    try:
        profiles = load_profiles()
        user_profile = profiles.get(current_user.get_id())
        
        if not user_profile:
            flash('User profile not found.', 'danger')
            return redirect(url_for('main.index'))
            
        return render_template('profile.html', 
                             profile=user_profile,
                             username=current_user.username)

    # Handle exception
    except Exception as e:
        logging.error(f"Error in profile_view: {str(e)}")
        flash('An error occurred while loading profile.', 'danger')
        return redirect(url_for('main.index'))

@main.route('/update_preferences', methods=['GET', 'POST'])
@login_required

# --- Function: update_preferences ---
def update_preferences():
    """Handle preferences update."""
    # Begin try block
    try:
        profiles = load_profiles()
        user_profile = profiles.get(current_user.get_id())
        
        if not user_profile:
            flash('User profile not found.', 'danger')
            return redirect(url_for('main.index'))

        if request.method == 'POST':
            # Update profile fields
            fields = [
                'email', 'phone_number', 'learning_style', 'preferred_subject', 'goals', 'hobbies', 'strengths', 
                'weaknesses', 'language', 'age', 'academic_level', 'cultural_background',
                'answer_type_weights[informational]', 'answer_type_weights[real_world]', 'answer_type_weights[cause_and_effect]', 'answer_type_weights[goal_based]'
            ]

            for field in fields:
                if field in request.form:
                    value = request.form.get(field)
                    print(field,": ",value)

                    # Check answer_type_weights first
                    if field == 'answer_type_weights[informational]' or field == 'answer_type_weights[real_world]' or field == 'answer_type_weights[cause_and_effect]' or field == 'answer_type_weights[goal_based]':
                        # Strip answer_type_weights from field
                        user_profile['answer_type_weights'][field[20:-1]] = float(value)
                    else:
                        if field == 'age':
                            user_profile[field] = int(value)
                        elif isinstance(value, str):
                            user_profile[field] = value.strip()
                        elif isinstance(value, list):
                            user_profile[field] = [item.strip() for item in value if item.strip()]

            save_profiles(profiles)
            flash('Your preferences have been updated.', 'success')
            return redirect(url_for('main.profile_view'))

        return render_template('update_preferences.html', 
                             profile=user_profile,
                             username=current_user.username)

    # Handle exception
    except Exception as e:
        logging.error(f"Error in update_preferences: {str(e)}")
        flash('An error occurred while updating preferences.', 'danger')
        return redirect(url_for('main.profile_view'))

@main.route('/clear_history', methods=['POST'])
@login_required

# --- Function: clear_history ---
def clear_history():
    """Clear the user's question history."""
    # Begin try block
    try:
        profiles = load_profiles()
        user_profile = profiles.get(current_user.get_id())
        
        if not user_profile:
            flash('User profile not found.', 'danger')
            return redirect(url_for('main.index'))

        user_profile['histories'] = []
        save_profiles(profiles)
        flash('Your history has been cleared.', 'success')
        return redirect(url_for('main.history_view'))

    # Handle exception
    except Exception as e:
        logging.error(f"Error in clear_history: {str(e)}")
        flash('An error occurred while clearing history.', 'danger')
        return redirect(url_for('main.history_view'))

@main.route('/ask', methods=['GET', 'POST'])
@login_required

# --- Function: ask_question ---
def ask_question():
    """Handle custom question asking using Gemini."""
    # Begin try block
    try:
        if request.method == 'POST':
            question = request.form.get('question', '').strip()
            if not question:
                flash('Please enter a question.', 'warning')
                return redirect(url_for('main.ask_question'))
                
            profiles = load_profiles()
            user_profile = profiles.get(current_user.get_id())
            if not user_profile:
                flash('User profile not found.', 'danger')
                return redirect(url_for('main.index'))

            # Get AI-generated answer using Gemini API
            answer = get_ai_answer(
                question,
                user_profile,
                current_app.config.get('KEYWORD_SYNONYMS', {})
            )
            if not answer:
                logging.warning("Gemini API returned no answer; using fallback answer.")
                fallback_answers = [
                    "I'm having trouble accessing the knowledge base. Please try again later.",
                    "Let me check my resources and get back to you on that.",
                    "I need to consult my reference materials. Could you rephrase the question?"
                ]
                answer = random.choice(fallback_answers)

            return render_template('response.html', 
                                   response=answer,
                                   question=question,
                                   username=current_user.username)

        return render_template('ask.html', username=current_user.username)

    # Handle exception
    except Exception as e:
        logging.error(f"Error in ask_question: {str(e)}")
        flash('An error occurred while processing your question.', 'danger')
        return redirect(url_for('main.index'))
    
@main.route('/leaderboard')
@login_required

# --- Function: leaderboard ---
def leaderboard():
    """Display the XP leaderboard."""
    profiles = load_profiles()
    # Sort users by XP in descending order and select the top 10
    sorted_users = sorted(profiles.values(), key=lambda u: u.get('xp', 0), reverse=True)
    top_users = sorted_users[:10]

    return render_template('leaderboard.html', users=top_users)

@main.route('/check_understanding', methods=['POST'])
@login_required

# --- Function: check_understanding ---
def check_understanding():
    """Compare user's summary to original content and score similarity"""
    data = request.get_json()
    if not data:
        return jsonify({"error": "Invalid request data"}), 400
        
    original_content = data.get('original_content', '')
    user_summary = data.get('user_summary', '')
    use_ai = data.get('use_ai', False)  # Get the toggle value
    
    if not original_content or not user_summary:
        return jsonify({"error": "Missing required content"}), 400
    
    # Begin try block
    try:
        if use_ai:
            # Use AI-based scoring when requested
            from services.answer_service import assess_understanding
            result = assess_understanding(original_content, user_summary)
            source = "AI"
        else:
            # Use local similarity scoring by default
            result = calculate_similarity_score(original_content, user_summary)
            source = "local"
            
        result['source'] = source
        
        # adjust user weights based on similarity score
        from services.similarity_service import adjust_answer_weights
        from data.data import load_profiles, save_profiles
        
        profiles = load_profiles()
        user_profile = profiles.get(current_user.get_id())
        
        if user_profile:
            answer_style = data.get('answer_style', '')  
            updated_profile = adjust_answer_weights(user_profile, result['score'], answer_style)
            profiles[current_user.get_id()] = updated_profile
            save_profiles(profiles)
            
            result['weights'] = user_profile['answer_type_weights']
            result['weight_adjustment_count'] = user_profile['weight_adjustment_count']
        
        logging.info(f"Similarity score ({source}): {result['score']}")
        return jsonify(result)
        
    # Handle exception
    except Exception as e:
        logging.error(f"Understanding check error: {str(e)}")
        return jsonify({"error": "Failed to evaluate understanding"}), 500