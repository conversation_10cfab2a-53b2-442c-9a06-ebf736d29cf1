# services/auto_tagging.py

# --- Function: generate_tags ---
def generate_tags(answer_text):
    tags = []
    lowered = answer_text.lower()
    if "step" in lowered or "first," in lowered:
        tags.append("step-by-step")
    if "example" in lowered or "for example" in lowered:
        tags.append("example")
    if "goal" in lowered or "objective" in lowered:
        tags.append("goal-based")
    if "because" in lowered or "cause" in lowered:
        tags.append("cause-and-effect")
    if "real life" in lowered or "everyday" in lowered:
        tags.append("real-world")
    if "definition" in lowered or "means" in lowered:
        tags.append("informational")

    return tags[:5]  # limit to 5