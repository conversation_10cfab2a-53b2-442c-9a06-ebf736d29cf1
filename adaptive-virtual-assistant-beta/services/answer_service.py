# services/answer_service.py
import logging
from config import Config
from google import genai
import re

# Initialize the Gemini client with API key
client = genai.Client(api_key=Config.GEMINI_API_KEY)


# --- Function: process_question_keywords ---
def process_question_keywords(question, keyword_synonyms):
    """Return matched keywords from the question using provided synonyms."""
    words = question.split()
    return [word for word in words if word.lower() in keyword_synonyms] or words


# --- Function: format_user_history ---
def format_user_history(histories, max_entries=5):
    """Format recent history entries for context display."""
    if not histories:
        return "No previous interactions recorded."
    recent = histories[-max_entries:]
    return "Recent interactions:\n" + "\n".join(
        f"- {entry['timestamp']}: {entry['question'][:50]}..." for entry in recent
    )


# --- Function: detect_question_type ---
def detect_question_type(question):
    """Classify the question into fact, procedural, rationale, or unknown."""
    q = question.lower()
    if any(term in q for term in ['what', 'when', 'where', 'who']):
        return "Fact"
    elif any(term in q for term in ['how', 'steps', 'process']):
        return "Procedural"
    elif any(term in q for term in ['why', 'reason', 'explain']):
        return "Rationale"
    return "Unknown"


# --- Function: determine_best_answer_style ---
def determine_best_answer_style(user_profile):
    """
    Determine the preferred answer style based on the user's profile.
    Uses the 80-20 rule: 80% of the time choose the highest weighted style,
    20% of the time explore other styles based on their weights.
    """
    import random
    
    # Get adaptive weights from user profile
    adaptive_weights = user_profile.get('answer_type_weights', {
        "informational": 0.5,
        "real_world": 0.5,
        "cause_and_effect": 0.5,
        "goal_based": 0.5
    })
    
    # Apply the 80-20 rule
    if random.random() < 0.8:  # 80% of the time: exploitation
        return max(adaptive_weights.items(), key=lambda x: x[1])[0]
    else:  # 20% of the time: exploration
        highest_style = max(adaptive_weights.items(), key=lambda x: x[1])[0]
        exploration_weights = {k: v for k, v in adaptive_weights.items() if k != highest_style}
        
        total = sum(exploration_weights.values())
        if total == 0:  # Prevent division by zero
            return random.choice(list(exploration_weights.keys()))
            
        r = random.random() * total
        cumulative = 0
        for style, weight in exploration_weights.items():
            cumulative += weight
            if r <= cumulative:
                return style
        
        # Fallback (shouldn't reach here)
        return list(exploration_weights.keys())[0]


# --- Function: get_ai_answer ---
def get_ai_answer(question, user_profile, keyword_synonyms):
    """
    Main logic to build a context-aware, personalized AI prompt and return Gemini's response.
    """
    # Begin try block
    try:
        question_type = detect_question_type(question)
        preferred_answer_style = determine_best_answer_style(user_profile)
        detected_keywords = process_question_keywords(question, keyword_synonyms)

        # Build detailed user context
        context = f"""
        <OBJECTIVE_AND_PERSONA>
        You are an Adaptive Virtual Assistant created by Arizona State University Software Engineering Capstone Group 21.
        <OBJECTIVE_AND_PERSONA>
        
        <INSTRUCTIONS>
        Begin your response with: "This is a [{preferred_answer_style.replace('_', ' ').title()}] style answer to your question."
        Then follow these steps:
        1. State the detected question type.
        2. Explain why this answer format is suitable for this question type.
        3. Generate a {preferred_answer_style.replace('_', ' ').title()} style response.
        4. Ensure the response is appropriate for a {user_profile.get('learning_style', 'general')} learner.
        
        IMPORTANT: The response MUST begin by identifying that it's a {preferred_answer_style.replace('_', ' ').title()} style answer.
        </INSTRUCTIONS>
        
        <CONTEXT>
        The user can be described as the following:
        - ID: {user_profile.get('user_id')}
        - Username: {user_profile.get('username')}
        - Name: {user_profile.get('name')}
        - Email: {user_profile.get('email')}
        - Phone: {user_profile.get('phone_number')}
        - Age: {user_profile.get('age')}
        - Gender: {user_profile.get('gender')}
        - Academic Level: {user_profile.get('academic_level')}
        - Preferred Subject: {user_profile.get('preferred_subject')}
        - Learning Style: {user_profile.get('learning_style')}
        - Location: {user_profile.get('location')}
        - Language: {user_profile.get('language')}
        - Timezone: {user_profile.get('timezone')}
        - Cultural Background: {user_profile.get('cultural_background')}
        - Hobbies: {', '.join(user_profile.get('hobbies', []))}
        - Goals: {', '.join(user_profile.get('goals', []))}
        - Strengths: {', '.join(user_profile.get('strengths', []))}
        - Weaknesses: {', '.join(user_profile.get('weaknesses', []))}
        - XP: {user_profile.get('xp')}
        - Level: {user_profile.get('level')}
        - Streak: {user_profile.get('streak')}
        - Badges: {', '.join(user_profile.get('badges', []))}
        - Interaction History:
        {format_user_history(user_profile.get('histories', []))}
        </CONTEXT>

        <CONSTRAINTS>
        - Question: {question}
        - Type: {question_type}
        - Preferred Answer Style: {preferred_answer_style.replace('_', ' ').title()}
        - Detected Keywords: {', '.join(detected_keywords)}
        </CONSTRAINTS>
        """

        # Generate AI response using Gemini
        response = client.models.generate_content(
            model="gemini-2.0-flash",
            contents=context
        )

        return response.text if response.text else "Sorry, no answer was generated."

    # Handle exception
    except Exception as e:
        logging.error(f"Gemini API Error: {str(e)}")
        return "Sorry, there was an issue generating the answer. Please try again later."


# --- Function: assess_understanding ---
def assess_understanding(original_content, user_summary):
    """Compare user's summary to original content with strict evaluation of correctness."""
    # Begin try block
    try:
        # Send to Gemini API for comparison with stricter evaluation criteria
        prompt = f"""
        Compare the Original and User Summary texts for content similarity.

        IMPORTANT: If the two texts contain identical information (even if worded differently), 
        the score should be 100.0.

        Otherwise:
        - Start at 0.
        - Add points for each key concept from the Original that appears in the User Summary.
        - Give partial points for partially covered topics.
        - Ignore any extra information in the User Summary.
        - Give points for simple mention of topics related.
        - Max score is 100 if all key information is covered.

        Respond ONLY with: SCORE: [number with one decimal place, e.g., 100.0 or 47.0]

        Original: {original_content[:1500]}

        User Summary: {user_summary[:1500]}
        """
        
        response = client.models.generate_content(
            model="gemini-2.0-flash",
            contents=prompt
        )
        
        # Update the regex to capture decimals
        match = re.search(r'SCORE:\s*(\d+(?:\.\d{1})?)', response.text)
        score = round(float(match.group(1)), 1) if match else 0
        
        # Generate appropriate feedback
        feedback = ""
        if score >= 90:
            feedback = "Excellent! You've mastered this concept."
        elif score >= 75:
            feedback = "Great job! You have a solid understanding."
        elif score >= 60:
            feedback = "Good work! Try focusing on the main points."
        elif score >= 40:
            feedback = "You're making progress, but review the key concepts."
        else:
            feedback = "You may need to review this topic again."
        
        print(f"AI similarity score (before return): {score}")  # This prints to terminal
        logging.info(f"AI similarity score (before return): {score}")  # This logs to file if configured
        return {
            "score": score,
            "feedback": feedback
        }
        
    # Handle exception
    except Exception as e:
        logging.error(f"Understanding assessment error: {str(e)}")
        raise