# services/similarity_service.py
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
import logging

# Try to download NLTK packages (only needs to happen once)
# Begin try block
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
# Handle exception
except Exception as e:
    logging.warning(f"NLTK download error: {str(e)}")


# --- Function: calculate_similarity_score ---
def calculate_similarity_score(original_content, user_summary):
    """Calculate similarity score between original content and user summary using TF-IDF and cosine similarity"""
    # Begin try block
    try:
        # Preprocess both texts for comparison
        processed_original = preprocess_text(original_content[:1000])
        processed_summary = preprocess_text(user_summary[:1000])
        
        # Create TF-IDF vectors
        vectorizer = TfidfVectorizer()
        tfidf_matrix = vectorizer.fit_transform([processed_original, processed_summary])
        
        # Calculate cosine similarity
        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
        
        # Convert to 0-100 scale with one decimal
        score = round(similarity * 100, 1)
        
        # Generate feedback based on score
        feedback = get_feedback_for_score(score)
        
        return {
            "score": score,
            "feedback": feedback
        }
    # Handle exception
    except Exception as e:
        logging.error(f"Similarity calculation error: {str(e)}")
        return {"score": 0, "feedback": "Error calculating similarity"}


# --- Function: preprocess_text ---
def preprocess_text(text):
    """Clean and normalize text for comparison"""
    # Begin try block
    try:
        # Tokenize and lowercase
        tokens = word_tokenize(text.lower())
        
        # Remove stopwords and keep only alphanumeric words
        stop_words = set(stopwords.words('english'))
        filtered_tokens = [word for word in tokens if word.isalnum() and word not in stop_words]
        
        # Lemmatize words to base form
        lemmatizer = WordNetLemmatizer()
        lemmatized = [lemmatizer.lemmatize(word) for word in filtered_tokens]
        
        return ' '.join(lemmatized)
    # Handle exception
    except Exception as e:
        logging.warning(f"Text preprocessing error: {str(e)}")
        return text.lower()  # Fallback to simple lowercase


# --- Function: get_feedback_for_score ---
def get_feedback_for_score(score):
    """Return appropriate feedback based on similarity score"""
    if score >= 90:
        return "Excellent! You've mastered this concept."
    elif score >= 75:
        return "Great job! You have a solid understanding."
    elif score >= 60:
        return "Good work! Try focusing on the main points."
    elif score >= 40:
        return "You're making progress, but review the key concepts."
    else:
        return "You may need to review this topic again."


# --- Function: adjust_answer_weights ---
def adjust_answer_weights(profile, similarity_score, answer_style):
    """
    Adjust user's answer type weights based on similarity score.
    Increases weights for good performance, decreases for poor performance.
    """
    # init tracking count if not present
    if 'weight_adjustment_count' not in profile:
        profile['weight_adjustment_count'] = 0
    
    # increment adjustment count
    profile['weight_adjustment_count'] += 1
    
    # calc learning rate
    base_learning_rate = 0.1
    learning_rate = base_learning_rate / (1 + profile['weight_adjustment_count'] / 25)
    
    # normalize 0-1
    normalized_score = similarity_score / 100
    
    if 'answer_type_weights' not in profile:
        profile['answer_type_weights'] = {
            "informational": 0.5,
            "real_world": 0.5,
            "cause_and_effect": 0.5,
            "goal_based": 0.5
        }
    
    current_weight = profile['answer_type_weights'].get(answer_style, 0.5)
    
    if similarity_score >= 60: 
        new_weight = current_weight * (1 - learning_rate) + normalized_score * learning_rate
    elif similarity_score <= 40: 
        penalty_factor = (40 - similarity_score) / 40  
        new_weight = current_weight * (1 - learning_rate * penalty_factor * 0.5)
    else:  
        new_weight = current_weight
    
    new_weight = max(0.1, min(1.0, new_weight))
    
    # update the weight in profile
    profile['answer_type_weights'][answer_style] = round(new_weight, 2)
    
    # log
    logging.info(f"Adjusted {answer_style} weight for user {profile.get('username', 'unknown')} from {current_weight} to {new_weight} (score: {similarity_score})")
    
    return profile