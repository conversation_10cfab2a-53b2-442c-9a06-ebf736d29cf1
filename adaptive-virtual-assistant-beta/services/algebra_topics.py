# services/algebra_topics.py
# Algebra I topics with improved responses

algebra_topics = {
    'quadratic equations': {
        'keywords': ['quadratic equations', 'quadratic formula', 'parabolas', 'roots of equations', 'completing the square', 'quadratic'],
        'responses': {
            "fighting": [
                """
                A quadratic equation is like planning a two-part combo to take down your opponent.
                The equation ax^2 + bx + c = 0 represents your strategy: ax^2 is the big opening move, bx is the setup, and c is your base stance.
                Your goal is to find the "roots" of the equation, which are the points where your strikes will land perfectly—where the equation equals zero.

                The quadratic formula, x = (-b ± √(b^2 - 4ac)) / 2a, is your secret weapon. The -b is your counter to their movement, resetting the situation and preparing you to strike.
                The ± √(b^2 - 4ac) represents analyzing their defense; the discriminant (b^2 - 4ac) reveals if an opening exists.
                A positive discriminant means there are two roots, or two perfect strike points; zero means one decisive strike; and a negative discriminant means no real openings to attack.
                Finally, the 2a ensures you balance your energy and precision, dividing your focus evenly to land your combo.
                Breaking it down step by step ensures you fight smarter, not harder, finding the right angles to hit your target effectively!
                """
            ],
            "hunting": [
                """
                Quadratic equations in hunting can be related to calculating the trajectory of a projectile, like an arrow or bullet, as it moves through the air. The path of a projectile under the influence of gravity generally follows a parabolic shape, which can be described using a quadratic equation.
                Imagine you're aiming at a deer that's 50 yards away, and you want to determine the point at which to aim so that your shot will hit the target accurately. The height y of the projectile as a function of the horizontal distance x can be modeled by a quadratic equation:
                x = (-b ± √(b² - 4ac)) / 2a
                To predict where your projectile will hit after traveling a certain distance you can plug in the value of distance the find the height at that point.
                """
            ],            
            "drawing": [
                """
                Imagine you're sketching the path of a ball you threw into the air, trying to draw it as it rises and falls back down to the ground. The quadratic formula helps determine the height and distance of that ball at any point in time. If we think about the formula:
                x = (-b ± √(b² - 4ac)) / 2a
                it allows us to find the moments when the ball starts (where the height is zero) and when it returns back down. When you draw this path, it usually takes the shape of a parabola, and the quadratic formula helps find exactly where the ball touches the ground, which is like solving for when the height is zero.
                """
            ],
            "soccer": [
                """
                Imagine kicking a soccer ball during a game, and it goes up in the air and comes back down. If you wanted to figure out when the ball will hit the ground or at what point it reaches a certain height, the quadratic formula can help you with that. The formula:
                x = (-b ± √(b² - 4ac)) / 2a
                allows you to calculate those points. Here, "a", "b", and "c" represent different factors like the strength of the kick, the angle, and initial height. This path of the ball forms a parabola, and the quadratic formula helps us find out exactly where it lands.
                """
            ],
            "reading": [
                """
                Imagine you are reading a story about a character who throws a stone off a cliff. The stone’s height changes over time, forming a curved path called a parabola. The quadratic formula helps determine when the stone will hit the ground or how high it will get at different moments. The formula is:
                x = (-b ± √(b² - 4ac)) / 2a
                This formula is like a key that unlocks the answer to where and when the stone will reach certain heights, making the story more interesting with calculations.
                """
            ],
            "hiking": [
                """
                While hiking, imagine you throw a rock up into the air from a mountain. The quadratic formula helps calculate when and where that rock will fall back down. The rock’s path forms a parabola, and the formula:
                x = (-b ± √(b² - 4ac)) / 2a
                tells you where it will land, considering your throwing strength and the height of the mountain. It’s useful to understand the motion of things falling during your adventures.
                """
            ],
            "painting": [
                """
                Imagine you’re painting a curve on a canvas and want to know exactly where it touches the "ground" (x-axis). The quadratic formula, x = (-b ± √(b² - 4ac)) / 2a
               , is like a set of instructions that tells you those exact points, or roots.
                """
            ],
            "default": [
                """
                Quadratic equations are equations of the form ax² + bx + c = 0, {name}.

                **Example:**

                - **Equation:** x² - 5x + 6 = 0.
                - **Factor:** (x - 2)(x - 3) = 0.
                - **Set each factor to zero:** x - 2 = 0 ⇒ x = 2; x - 3 = 0 ⇒ x = 3.

                So, the solutions are x = 2 and x = 3.""",
                """You can also use the quadratic formula to solve any quadratic equation, {name}:

                - **Quadratic Formula:** x = [-b ± √(b² - 4ac)] / (2a).
                - **Apply it to the equation:** x² + 4x + 3 = 0.
                """
            ]
        }
    },
    'solving linear equations': {
        'keywords': ['solve linear equations', 'linear equations', 'solving for x', 'simple equations', 'one-variable equations', 'linear functions'],
        'responses': [
            """
            {name}, let's solve a simple linear equation step by step:

            1. **Equation:** Let's say we have 2x + 3 = 7.
            2. **Subtract 3 from both sides:** 2x + 3 - 3 = 7 - 3 ⇒ 2x = 4.
            3. **Divide both sides by 2:** (2x)/2 = 4/2 ⇒ x = 2.

            So, x equals 2. This means if we replace x with 2 in the original equation, both sides are equal.""",
            """To solve for x, {name}, follow these steps:

            1. **Equation:** 5x - 10 = 15.
            2. **Add 10 to both sides:** 5x - 10 + 10 = 15 + 10 ⇒ 5x = 25.
            3. **Divide both sides by 5:** (5x)/5 = 25/5 ⇒ x = 5.

            Therefore, x equals 5.""",
        ]
    },
    'factoring': {
        'keywords': ['factoring', 'factorization', 'factor', 'factoring quadratics', 'prime factorization', 'common factors'],
        'responses': [
            """{name}, factoring is like breaking down a number or expression into parts that can be multiplied to get the original number or expression.

            **Example:**

            - **Expression:** x² + 5x + 6.
            - **Factors:** (x + 2)(x + 3).

            Because (x + 2)(x + 3) expands back to x² + 5x + 6.""",
            """Here's how to factor a quadratic equation step by step, {name}:

            1. **Identify a, b, and c in ax² + bx + c.** For x² + 7x + 12, a=1, b=7, c=12.
            2. **Find two numbers that multiply to ac (1*12=12) and add to b (7).** The numbers 3 and 4 work because 3*4=12 and 3+4=7.
            3. **Write the factors:** (x + 3)(x + 4).""",
        ]
    },
    'simplifying expressions': {
        'keywords': ['simplify expressions', 'simplifying expressions', 'like terms', 'combine like terms', 'simplify algebraic expressions', 'distributive property'],
        'responses': [
            """Simplifying expressions means combining like terms and making the expression as concise as possible, {name}.

            **Example:**

            - **Expression:** 3x + 5x - 2.
            - **Combine like terms:** (3x + 5x) - 2 ⇒ 8x - 2.""",
            """Using the distributive property helps in simplifying expressions, {name}.

            **Example:**

            - **Expression:** 2(x + 3).
            - **Distribute 2:** 2*x + 2*3 ⇒ 2x + 6.""",
        ]
    },
    'functions': {
        'keywords': ['functions', 'domain', 'range', 'function notation', 'evaluate functions', 'input', 'output'],
        'responses': [
            """A function is a special relationship where each input has a single output, {name}.

            **Example:**

            - **Function:** f(x) = x + 2.
            - **Evaluate at x = 3:** f(3) = 3 + 2 = 5.

            So, the output is 5 when the input is 3.""",
            """Understanding functions is key to exploring advanced math topics, {name}.

            - **Domain:** All possible input values.
            - **Range:** All possible output values.""",
        ]
    },
    'inequalities': {
        'keywords': ['inequalities', 'solve inequalities', 'graphing inequalities', 'linear inequalities', 'greater than', 'less than'],
        'responses': [
            """Inequalities show that one expression is larger or smaller than another, {name}.

            **Example:**

            - **Inequality:** x + 3 > 5.
            - **Subtract 3 from both sides:** x > 2.

            So, x can be any number greater than 2.""",
            """When solving inequalities, remember that multiplying or dividing by a negative number reverses the inequality sign, {name}.

            **Example:**

            - **Inequality:** -2x < 6.
            - **Divide both sides by -2 (and reverse sign):** x > -3.""",
        ]
    },
    'exponents': {
        'keywords': ['exponents', 'powers', 'laws of exponents', 'exponential expressions', 'bases', 'exponential growth'],
        'responses': [
            """An exponent tells you how many times to multiply a number by itself, {name}.

            **Example:**

            - **Expression:** 2³.
            - **Calculation:** 2 * 2 * 2 = 8.""",
            """Understanding the laws of exponents can simplify complex expressions, {name}.

            - **Product Rule:** a^m * a^n = a^(m+n).
            - **Quotient Rule:** a^m / a^n = a^(m-n).""",
        ]
    },
    'systems of equations': {
        'keywords': ['systems of equations', 'simultaneous equations', 'solve systems', 'linear systems', 'intersection point'],
        'responses': [
            """Systems of equations involve finding values that satisfy multiple equations at once, {name}.

            **Example:**

            - **Equations:**
            1. x + y = 5
            2. x - y = 1
            - **Add equations:** (x + y) + (x - y) = 5 + 1 ⇒ 2x = 6 ⇒ x = 3.
            - **Solve for y:** 3 + y = 5 ⇒ y = 2.""",
            """Graphically, the solution is where the lines intersect, {name}.""",
        ]       
    },
   "real numbers": {
        "keywords": ["real number", "real numbers", "real value", "number line", "positive numbers", "negative numbers", "fractions", "decimals"],
        "responses": {
            'drawing': [
                "Think of real numbers like the colors on your palette, {name}. Each real number is like a different shade, blending into the next without any jumps—just as colors smoothly transition from one to another. Some colors are simple and well-defined, like red or blue, similar to whole numbers. Others are unique blends, like teal or lavender, representing decimals and irrational numbers. Together, they create a continuous spectrum that you can use to create anything on your canvas, much like real numbers cover every possible point on the number line. The opposite of this would be imaginary numbers. In our example, colors that humans cannot see would represent imaginary numbers."
            ],
            'soccer': [
                "Real numbers are like the points you score in soccer, {name}. They can be positive or negative, and each number has its own place on the number line, like players on the field."
            ],
            "default": [
                """Real numbers include all the numbers that can be found on the number line, {name}. This encompasses both rational and irrational numbers.

                **Examples:**
                
                - **Positive Real Numbers:** 3, 4.5, 0.75
                - **Negative Real Numbers:** -2, -5.6, -0.33
                - **Zero:** 0

                Real numbers are used to measure continuous quantities and can represent distances, weights, and other measurable attributes.""",
                
                """Real numbers consist of both rational and irrational numbers, {name}. They can be whole numbers, fractions, or decimals.

                **Properties:**
                
                # Handle exception
                - **Closure:** Real numbers are closed under addition, subtraction, multiplication, and division (except by zero).
                - **Density:** Between any two real numbers, there exists another real number.
                - **Uncountability:** There are infinitely many real numbers, even within a finite interval.""",
            ]
        }
    },
    "imaginary numbers": {
        "keywords": ["imaginary numbers", "complex numbers", "i", "square roots of negative numbers", "pure imaginary numbers"],
        "responses": {
            "drawing": [
            "Think of imaginary numbers as colors beyond the visible spectrum, {name}. Just like ultraviolet or infrared light that artists can't see but can represent, imaginary numbers aren't on the real number line but help create more complex and vibrant mathematical 'artworks'. They add an extra dimension to your creative toolkit, allowing you to explore concepts beyond the ordinary canvas."
            ],
            "soccer": [
                "Imagine you're playing soccer on a field that extends into a new dimension, {name}. The real numbers are like the positions on the ground—forward, backward, left, and right. Imaginary numbers add an upward dimension, like being able to play soccer vertically into the air. While we can't play soccer this way in reality, imaginary numbers let us solve problems that require this extra 'height' in mathematics."
            ],
            "default": [
                """Imaginary numbers are numbers that, when squared, give a negative result, {name}. They are based on the imaginary unit **i**, where **i² = -1**.

                **Examples:**
                
                - **Basic Imaginary Number:** i
                - **Multiple of i:** 3i, -2i, 4.5i

                Imaginary numbers are used in various fields such as engineering, physics, and applied mathematics to solve equations that have no real solutions.""",
                
                """Imaginary numbers are a subset of complex numbers, {name}. A complex number has a real part and an imaginary part, expressed as **a + bi**.

                **Properties:**
                
                - **Addition:** (a + bi) + (c + di) = (a + c) + (b + d)i
                - **Multiplication:** (a + bi)(c + di) = (ac - bd) + (ad + bc)i
                - **Conjugate:** The conjugate of (a + bi) is (a - bi)

                Imaginary numbers help in representing and solving oscillatory and wave-related phenomena.""",
            ]
        }
    },
    "rational numbers": {
        "keywords": ["rational numbers", "rational number", "fractions", "ratios", "terminating decimals", "repeating decimals"],
        "responses": {
            "drawing": [
                "Rational numbers are like the structured patterns in your artwork. Imagine creating a grid or repeating motifs, like evenly spaced dots, stripes, or geometric shapes. Just as these patterns can be replicated with precise measurements, rational numbers are those that can be written as fractions and either terminate or repeat when converted to decimals. They bring order and predictability to the number line, much like symmetry or consistency adds balance to your drawings."
            ],
            "soccer": [
                "Think of rational numbers as the calculated passes or movements on the soccer field. Each pass can be measured and planned, whether it’s a consistent short pass repeated in a drill or a finite, precise long shot to a teammate. These structured actions mirror rational numbers, which are finite or repeating when expressed in decimals. They’re the opposite of chaotic, random play—bringing control and strategy to the game, just as rational numbers bring structure to mathematics."
            ],
            "default": [
                """Rational numbers are numbers that can be expressed as the ratio of two integers, {name}, where the denominator is not zero.

                **Examples:**
                
                - **Positive Rational Numbers:** 1/2, 3/4, 5/1
                - **Negative Rational Numbers:** -2/3, -7/5
                - **Whole Numbers:** 4 (which is 4/1), 0 (which is 0/1)

                Rational numbers include integers, finite decimals, and repeating decimals.""",
                
                """**Properties of Rational Numbers:**
                
                # Handle exception
                - **Closure:** Rational numbers are closed under addition, subtraction, multiplication, and division (except by zero).
                - **Density:** Between any two rational numbers, there exists another rational number.
                - **Repeating or Terminating Decimals:** When expressed in decimal form, rational numbers either terminate or have a repeating pattern."""
            ]
        }
    },
    "irrational numbers": {
        "keywords": ["irrational numbers", "irrational number", "non-repeating decimals", "non-terminating decimals", "√2", "π", "e"],
        "responses": {
            "drawing": [
                "Picture irrational numbers as unique textures or patterns in your art, {name}, like natural textures that never perfectly repeat. These numbers can't be captured as simple fractions, and their decimals go on infinitely without a pattern, much like complex textures in nature that add a bit of mystery to your art."
            ],
            "soccer": [
                "Imagine you’re playing soccer, and you’re working on pinpointing the exact halfway mark between the goalposts. You can measure it pretty precisely, but it might land on something like 15.782... meters. No matter how hard you try, you can’t write down this exact distance as a simple fraction or a clean decimal. It keeps going with new numbers without repeating, like an endless dribble. Irrational numbers are just like that distance: they can’t be fully captured as a simple ratio of two whole numbers, and their decimal form goes on forever without any repeating pattern. Examples are numbers like √2 or π—distances or values in math that we can estimate but never truly pin down exactly, much like an endless run up the field."
            ],
            "default": [
                """Irrational numbers are numbers that cannot be expressed as the ratio of two integers, {name}. Their decimal expansions are non-terminating and non-repeating.

                **Examples:**
                
                - **√2:** Approximately 1.41421356...
                - **π (Pi):** Approximately 3.14159265...
                - **e (Euler's Number):** Approximately 2.71828182...

                Irrational numbers are essential in mathematics, especially in geometry, calculus, and number theory.""",
                
                """**Properties of Irrational Numbers:**
                
                - **Non-Repeating:** Their decimal representations never repeat a sequence of digits.
                - **Non-Terminating:** Their decimal expansions go on infinitely without terminating.
                - **Uncountable:** There are infinitely many irrational numbers, even within any finite interval.

                Irrational numbers cannot be precisely expressed as fractions, making them distinct from rational numbers."""
            ]
        }
    },
    "absolute value": {
        "keywords": ["absolute value", "magnitude", "distance from zero", "|x|", "positive distance"],
        "responses": {
            "drawing": [
                """
                Think of absolute value as the positive distance from a starting point, {name}. When creating art, you measure space and shapes without worrying about direction – it’s all about the total distance covered.

                **Examples:**
                
                - **|5| = 5** (distance 5 units)
                - **|-3| = 3** (distance 3 units)

                Whether you go left or right, the absolute value keeps it all positive!
                """
            ],
            "soccer": [
                """
                In soccer, absolute value represents the total distance covered, {name}, no matter which direction you're running. If you sprint 20 yards forward or backtrack 20 yards, you've still run 20 yards!

                **Examples:**
                
                - **|20| = 20** (forward 20 yards)
                - **|-15| = 15** (backward 15 yards)

                In absolute value, only the total movement counts, not the direction.
                """
            ],
            "reading": [
                """
                Absolute value is like measuring the pages you’ve read, {name}. Whether you start from the beginning or the end, the page count is the same.

                **Examples:**
                
                - **|25| = 25** (forward 25 pages)
                - **|-10| = 10** (back 10 pages)

                With absolute value, it’s all about how much you've covered, not where you started or the direction.
                """
            ],
            "hiking": [
                """
                Think of absolute value as the elevation change on a hike, {name}. Whether you climb up 1000 feet or descend 1000 feet, you’re still covering 1000 feet in altitude change.

                **Examples:**
                
                - **|1000| = 1000** (climb 1000 feet)
                - **|-500| = 500** (descend 500 feet)

                Absolute value measures the distance, ignoring if it’s uphill or downhill.
                """
            ],
            "default": [
                """The absolute value of a number is its distance from zero on the number line, {name}. Absolute values are always non-negative.

                **Examples:**
                
                - **|5| = 5**
                - **|-3| = 3**

                Absolute value is useful when only the magnitude matters, not the direction or sign.""",
                
                """When solving absolute value equations, remember that both positive and negative solutions are possible, {name}.

                **Example:**
                
                - **Equation:** |x - 2| = 5
                - **Solutions:** x - 2 = 5 or x - 2 = -5
                - **Solve:** x = 7 or x = -3

                This means x can be 7 or -3 to satisfy the equation."""
            ]
        }
    },
    "logarithms": {
        "keywords": ["logarithms", "log", "exponents", "inverse of exponentiation", "log rules"],
        "responses": [
            """A logarithm is the inverse of exponentiation, {name}. It answers the question, "To what power should the base be raised to get this number?"

            **Example:**
            
            - **Equation:** 10³ = 1000 can be rewritten as log₁₀(1000) = 3.
            
            Common logarithms have bases 10 or e, known as natural logarithms.""",
            
            """Logarithms follow specific rules that make complex expressions simpler, {name}.

            - **Product Rule:** logₐ(xy) = logₐ(x) + logₐ(y)
            - **Quotient Rule:** logₐ(x/y) = logₐ(x) - logₐ(y)
            - **Power Rule:** logₐ(xⁿ) = n * logₐ(x)

            These rules are useful for solving exponential equations.""",
        ]
    },
    "integers": {
        "keywords": ["integer", "integers", "whole number", "negative number", "positive number", "counting numbers"],
        "responses": {
            "drawing": [
                "Think of integers like the solid lines you use to frame your artwork, {name}. Each integer is a distinct point along a line, like individual brushstrokes spaced at regular intervals. Some are positive, like strokes moving to the right, and some are negative, moving to the left. But unlike colors blending smoothly, integers are separate, spaced apart, with nothing in between."
            ],
            "soccer": [
                "Imagine each integer as a goal scored in a soccer game, {name}. Goals count up as positive numbers as your team scores goals. Just like goals, each integer is a distinct score—no half-points or fractions in between! However, an integer can also be negative unlike the number of goals scored in a game!"
            ],
            "default": [
                """Integers are whole numbers, {name}, ranging from negative to positive, without fractions or decimals.

                **Examples:**
                
                - **Positive Integers:** 1, 2, 3
                - **Negative Integers:** -1, -2, -3
                - **Zero:** 0

                Integers are useful for counting and ordering things in clear, complete units, like levels or rounds."""
            ]
        }
    },
    "Balance Equations": {
        "keywords": ["balance", "solve for x", "4x + 8 = 24"],
        "responses": {
            "default": [
                """
                Isolate terms with 𝑥:
                To remove the 8, we do the opposite operation on both sides (subtract 8):
                4𝑥+8−8=24−8
                
                This simplifies to:
                4x=16
                
                Solve for 𝑥:
                Now, to isolate 𝑥, divide both sides by 4:
                4𝑥/4=16/4
                
                This gives:
                𝑥=4
                """
            ]
        }
    },
    "Order Of Operations": {
        "keywords": ["order of operations", "solve equation", "PEMDAS"],
        "responses": {
            "fighting": [
                """
                    If you’re into fighting, think of the order of operations in math like following the steps in a fight combo.
                    You need to perform each move in the right order to get the best result. If you mess up the sequence,
                    your combo might fail—or in math, your answer will be wrong!

                    Imagine you’re throwing a combo:

                    Parentheses: Duck to avoid a punch and step into position.
                    Exponents: Power up with a heavy strike (e.g., a roundhouse kick).
                    Multiplication/Division: Follow up with rapid strikes (punch-punch or jab-cross).
                    Addition/Subtraction: Finish with a knockout uppercut or a calculated retreat.
                    If you don’t follow the order, your moves might not land, or worse, you might leave yourself open. Same goes for math—stick to the order, and you’ll always win the match!
                    """
            ],
            "drawing": [
                """
                To solve an equations, you must solve the equation in the following order...
                The order of operations can be remembered using this art inspired acronym!
                Pencils (Parenthesis),
                Erase (Exponents),
                Marks (Multiplication),
                Dark (Division),
                And (Addition),
                Smooth (Subtraction).
                """
            ],
            "soccer": [
                """
                To solve an equations, you must solve the equation in the following order...
                The order of operations can be remembered using this soccer inspired acronym!
                Players (Parenthesis),
                Earn (Exponents),
                Medals (Multiplication),
                Defending (Division),
                Against (Addition),
                Strikers (Subtraction).
                """
            ],
            "reading": [
                """
                To solve an equations, you must solve the equation in the following order...
                The order of operations can be remembered using this reading-inspired acronym!
                Pages (Parentheses),
                Enrich (Exponents),
                Minds (Multiplication),
                Delving (Division),
                And (Addition),
                Studying (Subtraction).
                """
            ],
            "hiking": [
                """
                To solve an equations, you must solve the equation in the following order...
                The order of operations can be remembered using this hiking-inspired acronym!
                Peaks (Parentheses),
                Elevate (Exponents),
                Mountains (Multiplication),
                Descend (Division),
                Across (Addition),
                Summits (Subtraction).
                """
            ],
            "managing": [
                """
                To solve an equations, you must solve the equation in the following order...
                Think of it like managing a project, {name}. First, you 
                Plan (Parentheses) your tasks, then 
                Estimate (Exponents) resources, 
                Manage (Multiplication) the team, 
                Delegate (Division) responsibilities, 
                Add (Addition) up the progress, and 
                Subtract (Subtraction) any setbacks.
                """
            ],
            "painting": [
                """
                To solve an equations, you must solve the equation in the following order...
                The order of operations can be remembered using this hiking-inspired acronym!
                Painters (Parentheses),
                Enhance (Exponents),
                Masterpieces (Multiplication),
                During (Division),
                Artistic (Addition),
                Sessions (Subtraction).
                """
            ],
            "default": [
                """
                To solve an equations, you must solve the equation in the following order...
                Parentheses – Solve anything in parentheses first.
                Exponents – Next, calculate powers and roots.
                Multiplication and Division – Work from left to right.
                Addition and Subtraction – Finally, complete these from left to right.
                """
            ]
        }
    },
    "Foil": {
        "keywords": ["foil", "foiling", "reverse factoring"],
        "responses": {
            "drawing": [
                """
                Imagine palette 1 has colors A and B, and imagine palette 2 has colors c and d.
                The goal is to find all different color mixes you can make by combining one color from each palette.
                
                First: Mix the first colors from both palettes (A and C).
                Outer: Mix the first from Palette 1 with the last from Palette 2 (A and D).
                Inner: Mix the last from Palette 1 with the first from Palette 2 (B and C).
                Last: Mix the last colors from both palettes (B and D).

                This is just like FOIL in math:
                First, Outer, Inner, Last.

                The final step is to all all the values together.
                """
            ],
            "soccer": [
                """
                Imagine a situation where a team is setting up a combination play to bypass the defense with two forwards (A and B) and two defenders (X and Y). When attacking, the plays can go as follows:

                First: Forward A passes to Defender X (first pair interaction).
                Outside: Forward A then passes to Defender Y, creating a wide pass or an outside movement.
                Inside: Forward B now interacts with Defender X, cutting into the play.
                Last: Finally, Forward B passes to Defender Y, completing the play.
                
                This combination mirrors the FOIL steps: each part of the sequence combines different pairs to cover all interaction angles, much like multiplying each term in two binomials to get the full expression.
                """
            ],
            "reading": [
                """
                To explain the FOIL method using reading, think of it as a process of combining elements from two different stories or characters to create a fuller picture:

                First: The main character from Book 1 interacts with the main character from Book 2 (First terms).
                Outside: The main character from Book 1 interacts with the side character from Book 2 (Outside terms).
                Inside: The side character from Book 1 interacts with the main character from Book 2 (Inside terms).
                Last: Finally, the side character from Book 1 interacts with the side character from Book 2 (Last terms).
                
                Just as FOIL multiplies parts of two binomials to create a complete expression, combining these interactions builds a deeper, richer story. This is like how authors weave character interactions to expand themes, much like the way terms expand in algebra.
                """
            ],
            "hiking": [
                """
                Imagine you're planning a hike and need to choose between two options in two categories:

                Trails: You can hike either Trail A or Trail B.
                Gear: You can bring either a Map or a Compass.
                The FOIL method helps you consider all possible combinations of these choices:

                First (F): Trail A with a Map.
                Outside (O): Trail A with a Compass.
                Inside (I): Trail B with a Map.
                Last (L): Trail B with a Compass.
                Here's how it maps to the FOIL steps:

                First Terms: Combining the first option of each category (Trail A + Map).
                Outside Terms: Combining the first option of the first category with the second option of the second category (Trail A + Compass).
                Inside Terms: Combining the second option of the first category with the first option of the second category (Trail B + Map).
                Last Terms: Combining the second options of both categories (Trail B + Compass).
                Just like in algebra, where the FOIL method multiplies two binomials to find all possible products, this hiking analogy helps you see all possible gear and trail combinations. It ensures you consider every pairing so you're fully prepared for your hike.
                """
            ],
            "fighting": [
                """
                Alright, let's get ready to fight—math-style! The FOIL method is a bit like throwing a combo of punches when you’re multiplying two binomials. Each term needs to land in the right place to finish the job. Here's how you do it:
                The FOIL Technique

                When you multiply two binomials, like (a+b)(c+d)(a+b)(c+d), FOIL helps you make sure each term gets its turn to multiply with each other. "FOIL" stands for First, Outside, Inside, Last:

                First - Multiply the first terms in each binomial.
                Outside - Multiply the outside terms.
                Inside - Multiply the inside terms.
                Last - Multiply the last terms in each binomial.

                Let’s try it out with an example:
                Example

                Let's say you have (x+3)(x+5)(x+3)(x+5). Each of these is a binomial (two terms). Follow the FOIL steps:

                First: Multiply the first terms in each binomial.
                x⋅x=x2
                x⋅x=x2

                Outside: Multiply the outside terms.
                x⋅5=5x
                x⋅5=5x
            
                Inside: Multiply the inside terms.
                3⋅x=3x
                3⋅x=3x
            
                Last: Multiply the last terms in each binomial.
                3⋅5=15
                3⋅5=15

                After you’ve done each "punch" (multiplication), put it all together by combining the results:
                x2+5x+3x+15
                x2+5x+3x+15
                
                Now, combine any like terms (terms with the same power of xx):
                x2+8x+15
                x2+8x+15
                Final Answer
                (x+3)(x+5)=x2+8x+15
                (x+3)(x+5)=x2+8x+15
                
                And that’s it! You’ve completed the FOIL combo and come out with the final result. It’s a quick way to "fight" through the problem and get the answer accurately.
                
                Ready for another round?
                """
            ],
            "chess": [
                """
                In FOIL, each term pairing is like planning a sequence of moves on the board, each with a purpose. Let's break it down as if we’re executing a four-move combination.

                FOIL as a Four-Move Combination:
                Consider expanding ((a + b)(c + d)):

                1. First: Start with the first move, multiplying the first term of each binomial. This is like positioning your pieces early in the game, setting the tone for the expansion.  
                   - Move: a*c = ac
                
                2. Outer: Next, go for the outer pairing, where we strategically connect the first term of the first binomial with the last term of the second. Imagine this as a long-range move, like a bishop cutting across to influence the board.  
                   - Move: a*d = ad
                
                3. Inner: Now, work with the inner terms, focusing on the central position. This is like a knight maneuver, closing in on the center and strengthening the core.  
                   - Move: bc = b*c
                
                4. Last: Finally, close with the last terms. This final move ties everything together, like a strong endgame sequence that locks in the win.  
                   - Move: b*d = bd

                Checkmate the Expression:
                Combine each "move" into the final expression:
                
                ac + ad + bc + bd
                
                This methodical approach ensures we’ve considered every “move” in expanding the expression, just as a thorough analysis in chess ensures every possibility is explored.
                """
            ],
            "painting": [
                """
                Imagine you’re painting a mural with two layers, each having two colors. FOIL helps you mix these colors in the right order to get the perfect combination.

                FOIL stands for First, Outside, Inside, Last, and here’s how it works in a painting context:
                First: Start by blending the first color from each layer, like the base tones of your mural. This sets the foundation.
                Outside: Next, combine the outer edges of each layer’s colors. This adds an outer contrast, balancing the mural.
                Inside: Then, blend the inner colors from each layer. This creates depth, adding richness to the central part of the mural.
                Last: Finally, mix the last colors from each layer. This unifies the mural, creating a cohesive, finished look.
                """
            ],
            "default": [
                """
                FOIL stands for First, Outside, Inside, Last, and these represent the four steps you take to multiply the two binomials. Here's a breakdown of how it works, using the binomials “(a + b)” and “(c + d)”:

                First: Multiply the first terms from each binomial. In this case, you multiply “a” and “c” to get ac.
                Outside: Multiply the outermost terms in the expression. For (a + b)(c + d), the outside terms are “a” and “d”, giving you ad.
                Inside: Multiply the innermost terms. Here, the inside terms are “b” and “c”, which gives you bc.
                Last: Multiply the last terms from each binomial. In this case, “b” and “d” are the last terms, giving you bd.

                Once you've multiplied these terms, you add them all together:
                (a + b)(c + d) = ac + ad + bc + bd
                The FOIL method is a simple and organized way to remember all the necessary multiplications when expanding two binomials. It ensures that no combinations are missed, leading to the correct expanded form of the expression.
                """
            ]
        }
    },
    'algebraic expressions': {
        'keywords': ['algebraic expressions', 'variables', 'terms', 'coefficients', 'simplify algebraic expressions'],
        'responses': {
            'drawing': [
                """
                Think of algebraic expressions like layers in a painting, {name}. Each term is like a color or brushstroke. For example, in the expression 3x + 2y, the '3x' and '2y' are individual elements that add depth to your work. Simplifying these expressions is like blending colors to make your art cleaner and more cohesive.
                """
            ],
            'soccer': [
                """
                Imagine an algebraic expression as the team strategy in soccer, {name}. Each variable represents a player, and their coefficients show how much effort they’re putting into the play. For example, in 2x + 3y, 'x' and 'y' are players, and their roles are multiplied by their effort levels (2 and 3). Simplifying the expression is like streamlining the strategy for the best results.
                """
            ],
            'reading': [
                """
                Think of algebraic expressions as sentences in a story, {name}. Each term represents a part of the sentence, with variables as characters and coefficients as their roles. Simplifying an expression is like editing the sentence to make it more concise and impactful.
                """
            ],
            'hiking': [
                """
                Imagine you're packing for a hike, {name}, and your backpack has compartments represented by variables like x and y. Each term in an algebraic expression is an item in your backpack, and coefficients show how much space they take up. Simplifying the expression is like reorganizing your backpack to make it lighter and easier to carry.
                """
            ],
            'default': [
                """
                Algebraic expressions are mathematical phrases that include numbers, variables, and operations, {name}. For example, 3x + 5 is an expression where:
                - 3 is the coefficient of x.
                - x is the variable.
                - 5 is a constant.

                Simplifying an expression means combining like terms and making it as concise as possible.
                """,
                """
                To simplify algebraic expressions, {name}, follow these steps:
                1. Combine like terms (e.g., 3x + 5x = 8x).
                2. Apply the distributive property if needed (e.g., 2(x + 3) = 2x + 6).
                """
            ]
        }
    },
    'slope-intercept form': {
    'keywords': ['slope-intercept form', 'y = mx + b', 'linear equations', 'graphing lines', 'slope', 'y-intercept'],
        'responses': {
            'drawing': [
                """
                Think of the coordinate plane as your canvas, {name}. The equation y = mx + b is like a guideline for sketching a straight line. Here, 'b' represents where you start your drawing on the y-axis—the initial point. The 'm' is the slope, determining the angle of your line, much like the direction of your brushstroke. A positive 'm' means your line ascends, and a negative 'm' means it descends. By adjusting 'm' and 'b', you create different lines, adding variety to your artwork.
                """
            ],
            'soccer': [
                """
                Imagine the soccer field as a grid, {name}, where the sidelines and goal lines form the axes. The slope-intercept form y = mx + b helps plan a straight path or pass. The 'b' is your starting position on the field (y-intercept), and 'm' is your movement strategy—the slope. A higher 'm' means a steeper path towards the goal. By calculating 'm' and 'b', you can strategize the best path to score.
                """
            ],
            'hiking': [
                """
                Think of a mountain trail map with grid lines, {name}. The slope-intercept form y = mx + b represents your hiking path. The 'b' is your starting elevation on the y-axis, and 'm' is the slope of the trail—how steep it is. A positive 'm' means you're ascending, while a negative 'm' means you're descending. This equation helps you understand the terrain and prepare for the hike.
                """
            ],
            'default': [
                """
                The slope-intercept form of a linear equation is y = mx + b, {name}, where:

                - **m** is the slope of the line (rise over run).
                - **b** is the y-intercept (the point where the line crosses the y-axis).

                **Example:**

                - **Equation:** y = 2x + 3
                - **Slope (m):** 2 (the line rises 2 units for every 1 unit it moves right)
                - **Y-intercept (b):** 3 (the line crosses the y-axis at (0, 3))

                This form makes it easy to graph linear equations and understand how changes in 'm' and 'b' affect the line's direction and position.
                """,
                """
                To graph a linear equation using the slope-intercept form, {name}:

                1. **Plot the y-intercept (b):** Start by marking the point (0, b) on the y-axis.
                2. **Use the slope (m):** From the y-intercept, use the slope to find another point. If m = rise/run, move up or down (rise) and right or left (run) accordingly.
                3. **Draw the line:** Connect the points with a straight line extending in both directions.

                **Example:**

                - **Equation:** y = -½x + 4
                - **Y-intercept (b):** 4 (plot point at (0, 4))
                - **Slope (m):** -½ (from (0, 4), move down 1 unit and right 2 units to plot the next point)
                """
            ]
        }
    }
}