# services/predefined_answers.py

import random
import re
import logging
from collections import defaultdict
from services.algebra_topics import algebra_topics  # Ensure this file exists and is correctly formatted

# Predefined answers based on age groups, learning style, and user preferences
predefined_answers_by_age = {
    'age_5': {
        'what is math': {
            'visual': "Hi {name}! Imagine you have {hobby}s. If you have 2 {hobby}s and you get 1 more, now you have 3!",
            'auditory': "Hi {name}! Let's sing a song about numbers: 1, 2, 3!",
            'kinesthetic': "Hi {name}! Let's count your fingers to learn math!",
            'default': "Hi {name}! Math is about counting things like your {hobby}s!"
        },
        'how does photosynthesis work': "In {location}, plants drink sunlight to grow big and strong, {name}! Isn't that cool?",
        'what is philosophy': "{name}, philosophy is thinking about big questions, like 'Why do we {hobby}?'",
        'what is art': "Art is when you create something beautiful, like when you draw your {hobby}, {name}!",
        'what is science': "Science helps us understand the world around us, {name}, just like when you explore with your {hobby}."
    },
    'age_12': {
        'what is math': "Hello {name}! Math helps us understand numbers and patterns, which is useful in many things, like your hobby of {hobby}.",
        'how does photosynthesis work': "Photosynthesis is how plants make their food from sunlight, water, and air, {name}.",
        'what is philosophy': "{name}, philosophy is the study of big ideas about life and how we think.",
        'what is art': "Art is a way to express your creativity, {name}, perhaps through your hobby of {hobby}.",
        'what is science': "Science is the study of the natural world, {name}, helping us learn about everything from stars to plants."
    },
    'age_18': {
        'what is math': "{name}, mathematics is the study of numbers, quantities, and shapes, essential in fields like {preferred_subject}.",
        'how does photosynthesis work': "Photosynthesis is the process by which plants convert sunlight into chemical energy, {name}. It's vital for life on Earth.",
        'what is philosophy': "{name}, philosophy involves critical thinking about existence, knowledge, and ethics.",
        'what is art': "Art encompasses various forms of creative expression, {name}, including painting, music, and literature.",
        'what is science': "Science is a systematic enterprise that builds and organizes knowledge in the form of testable explanations and predictions about the universe."
    },
    'age_adult': {
        'what is math': "{name}, mathematics is a fundamental discipline exploring concepts such as quantity, structure, space, and change. It can even help with {goal}.",
        'how does photosynthesis work': "Photosynthesis is a biochemical process converting light energy into chemical energy, sustaining plant life and, by extension, life on Earth.",
        'what is philosophy': "{name}, philosophy examines the fundamental nature of knowledge, reality, and existence.",
        'what is art': "Art is a diverse range of human activities involving creative imagination to express technical proficiency, beauty, emotional power, or conceptual ideas.",
        'what is science': "Science is the pursuit and application of knowledge and understanding of the natural and social world following a systematic methodology based on evidence."
    }
}

# Helper function to adjust response based on learning style and age

# --- Function: adjust_for_learning_style ---
def adjust_for_learning_style(response, learning_style, age):
    # Apply learning style adjustments only for users under 18
    if age < 18:
        if learning_style == 'visual':
            response += " Visualizing this can help you understand better."
        elif learning_style == 'auditory':
            response += " Saying it out loud or listening to an explanation might help."
        elif learning_style == 'kinesthetic':
            response += " Using physical objects or movements can make it clearer."
    return response

# General topics with keywords and responses
general_topics = {
    'math': {
        'keywords': ['math', 'mathematics'],
        'responses': [
            "{name}, understanding math can help you with {preferred_subject_lower}.",
            "Mathematics solves problems in everyday life, {name}, especially in {preferred_subject_lower}.",
            "Mastering math is important for many careers, {name}, including achieving your goal of {goal}."
        ]
    },
    'photosynthesis': {
        'keywords': ['photosynthesis', 'plants', 'plant growth', 'chlorophyll'],
        'responses': [
            "Photosynthesis is how plants make food using sunlight, {name}. This is essential in {location}.",
            "Plants convert sunlight into energy through photosynthesis, vital for {cultural_background} agriculture.",
            "Without photosynthesis, plants couldn't grow, {name}. It even affects your hobby of {hobby}."
        ]
    },
    'philosophy': {
        'keywords': ['philosophy', 'existence', 'knowledge', 'ethics', 'metaphysics'],
        'responses': [
            "Philosophy is the study of fundamental questions, {name}. It aligns with your interest in {preferred_subject_lower}.",
            "It's about thinking deeply, {name}, which suits your strength in {strength}.",
            "Philosophy encourages us to question and understand the world, helping with your goal of {goal}."
        ]
    },
    'art': {
        'keywords': ['art', 'painting', 'sculpture', 'drawing', 'music', 'dance', 'literature'],
        'responses': [
            "Art is a wonderful way to express creativity, {name}. It goes well with your hobby of {hobby}.",
            "Exploring art can enhance your skills in {preferred_subject_lower}, {name}.",
            "Art allows us to see the world from different perspectives, {name}."
        ]
    },
    'science': {
        'keywords': ['science', 'physics', 'chemistry', 'biology', 'astronomy', 'geology'],
        'responses': [
            "Science helps us understand how the world works, {name}. It's great that you're interested in {preferred_subject_lower}.",
            "Learning science can help you achieve your goal of {goal}, {name}.",
            "Science is all about curiosity and discovery, which matches your strength in {strength}."
        ]
    },
    'history': {
        'keywords': ['history', 'historical', 'past events', 'ancient', 'civilization'],
        'responses': [
            "History teaches us about past events, {name}, and how they shape our present.",
            "Understanding history can give you insights into {preferred_subject_lower}, {name}.",
            "Exploring history can be fascinating, especially with your interest in {hobby}, {name}."
        ]
    },
    'programming': {
        'keywords': ['programming', 'coding', 'computer science', 'software', 'development'],
        'responses': [
            "Programming is about creating instructions for computers, {name}. It's essential in today's technology-driven world.",
            "Learning programming can help you achieve your goal of {goal}, {name}.",
            "Your strength in {strength} can help you excel in programming, {name}."
        ]
    },
    'literature': {
        'keywords': ['literature', 'books', 'novels', 'poetry', 'writing'],
        'responses': [
            "Literature explores written works, {name}, and can deepen your understanding of {preferred_subject_lower}.",
            "Reading and writing can be a great way to express yourself, especially with your hobby of {hobby}.",
            "Your goal of {goal} aligns well with literature, {name}."
        ]
    },
    'sports': {
        'keywords': ['sports', 'soccer', 'basketball', 'tennis', 'athletics'],
        'responses': [
            "Sports are a great way to stay active and healthy, {name}. They also teach teamwork and discipline.",
            "Your hobby of {hobby} fits well with sports, {name}.",
            "Participating in sports can help you achieve your goal of {goal}, {name}."
        ]
    },
    'music': {
        'keywords': ['music', 'instrument', 'singing', 'guitar', 'piano'],
        'responses': [
            "Music is a universal language, {name}, and it's wonderful that you're interested in it.",
            "Learning music can enhance your creativity and even help with {preferred_subject_lower}, {name}.",
            "Your strength in {strength} is valuable in music, {name}."
        ]
    },
    'language learning': {
        'keywords': ['language', 'languages', 'linguistics', 'foreign language', 'bilingual'],
        'responses': [
            "Learning new languages can open up opportunities, {name}, and connect you with different cultures.",
            "Your interest in {preferred_subject_lower} complements language learning, {name}.",
            "Being multilingual can help you achieve your goal of {goal}, {name}."
        ]
    },
    # Existing topics should remain unchanged
}

# Combine algebra topics and general topics, prioritizing algebra topics
topics = {}
topics.update(algebra_topics)  # Algebra topics come first
topics.update(general_topics)    # Then general topics

# Function to get predefined answer based on age and user preferences

# --- Function: get_predefined_answer ---
def get_predefined_answer(age, question_normalized, profile):
    name = profile.get('name', 'User')
    hobbies = profile.get('hobbies', ['your hobby'])
    hobby = hobbies[0].lower() if hobbies else 'your hobby'
    location = profile.get('location', 'your area')
    preferred_subject = profile.get('preferred_subject', 'your favorite subject')
    preferred_subject_lower = preferred_subject.lower()
    learning_style = profile.get('learning_style', 'default').lower()
    goals = profile.get('goals', ['your goal'])
    goal = goals[0].lower() if goals else 'your goal'
    strengths = profile.get('strengths', ['your strength'])
    strength = strengths[0].lower() if strengths else 'your strength'
    
    if age <= 5:
        age_key = 'age_5'
    elif age <= 12:
        age_key = 'age_12'
    elif age <= 18:
        age_key = 'age_18'
    else:
        age_key = 'age_adult'

    predefined = predefined_answers_by_age.get(age_key, {})
    answer_template = predefined.get(question_normalized)

    if isinstance(answer_template, dict):
        # If learning style specific answers are available
        answer = answer_template.get(learning_style, answer_template.get('default'))
    else:
        answer = answer_template

    if answer:
        # Begin try block
        try:
            return answer.format(
                name=name,
                hobby=hobby,
                location=location,
                preferred_subject=preferred_subject,
                preferred_subject_lower=preferred_subject_lower,
                goal=goal,
                strength=strength
            )
        # Handle exception
        except KeyError as e:
            logging.error(f"Missing placeholder in predefined answer: {e}")
            return f"Oops! I encountered an error while generating the answer."
    else:
        return None

# Function to get topic response based on keywords and user preferences

# --- Function: get_topic_response ---
def get_topic_response(question_normalized, profile):
    name = profile.get('name', 'User')
    hobbies = profile.get('hobbies', ['your hobby'])
    hobby = hobbies[0] if hobbies else 'your hobby'
    preferred_subject = profile.get('preferred_subject', 'your subject')
    age = profile.get('age', 0)
    learning_style = profile.get('learning_style', 'default').lower()

    # Combine algebra topics (as primary) with any additional topics if needed.
    topics = {}
    topics.update(algebra_topics)
    matched_topic = None
    longest = 0
    for topic in topics.values():
        for keyword in topic['keywords']:
            if re.search(r'\b' + re.escape(keyword.lower()) + r'\b', question_normalized.lower()):
                if len(keyword) > longest:
                    matched_topic = topic
                    longest = len(keyword)
    if matched_topic:
        responses = matched_topic.get('responses')
        response_template = None
        if isinstance(responses, dict):
            response_template = responses.get('default')
            if isinstance(response_template, list):
                response_template = random.choice(response_template)
        elif isinstance(responses, list):
            response_template = random.choice(responses)
        if response_template:
            if age < 12:
                response_template = simplify_response_for_age(response_template)
            response_template = adjust_for_learning_style(response_template, learning_style, age)
            # Begin try block
            try:
                return response_template.format(name=name, hobby=hobby, preferred_subject=preferred_subject)
            # Handle exception
            except KeyError as e:
                logging.error(f"Missing placeholder: {e}")
                return "Error generating topic response."
    return None

# Function to simplify response for younger users

# --- Function: simplify_response_for_age ---
def simplify_response_for_age(response):
    replacements = {
        "quadratic equations": "math problems with x squared",
        "variables": "letters that stand for numbers",
        "expressions": "math phrases",
        "equations": "math sentences",
        "functions": "rules that match inputs to outputs",
        "inequalities": "comparisons using > or <"
    }
    for k, v in replacements.items():
        response = response.replace(k, v)
    return response


# --- Function: get_dynamic_answer ---
def get_dynamic_answer(profile: dict, question: str, user_history: list) -> str:
    """
    Generate a dynamic answer based on the user's profile, question, and history.
    """
    age = profile.get('age', 0)
    question_normalized = question.lower().strip('?.!').strip()
    for hobby in profile.get('hobbies', []):
        hobby_answer = get_predefined_hobby_answer(hobby.lower(), question_normalized, profile)
        if hobby_answer:
            logging.info(f"Found hobby-specific response for hobby: {hobby}")
            return hobby_answer
    answer = get_predefined_answer(age, question_normalized, profile)
    if answer:
        frequent = analyze_frequent_topics(user_history)
        if frequent:
            answer += " I see you're interested in " + ", ".join(frequent) + "."
        return answer
    answer = get_topic_response(question_normalized, profile)
    if answer:
        return answer
    return f"That's an interesting question, {profile.get('name', 'User')}! Let's explore it together."
    
    # Get general generated answer

# --- Function: get_predefined_answer ---
def get_predefined_answer(age, question_normalized, profile):
    name = profile.get('name', 'User')
    hobbies = profile.get('hobbies', ['your hobby'])
    hobby = hobbies[0] if hobbies else 'your hobby'
    preferred_subject = profile.get('preferred_subject', 'your subject')
    learning_style = profile.get('learning_style', 'default').lower()
    goals = profile.get('goals', ['your goal'])
    goal = goals[0] if goals else 'your goal'

    if age <= 5:
        age_key = 'age_5'
    elif age <= 12:
        age_key = 'age_12'
    elif age <= 18:
        age_key = 'age_18'
    else:
        age_key = 'age_adult'

    predefined = predefined_answers_by_age.get(age_key, {})
    answer_template = predefined.get(question_normalized)
    if isinstance(answer_template, dict):
        answer = answer_template.get(learning_style, answer_template.get('default'))
    else:
        answer = answer_template
    if answer:
        # Begin try block
        try:
            return answer.format(name=name, hobby=hobby, preferred_subject=preferred_subject, goal=goal)
        # Handle exception
        except KeyError as e:
            logging.error(f"Missing placeholder: {e}")
            return "Error generating answer."
    return None
    
    # Get general topic response
    answer = get_topic_response(question_normalized, profile)
    if answer:
        return answer
    
    # Default response if no match found
    return f"That's an interesting question, {profile.get('name', 'User')}! Let's explore it together."


# --- Function: analyze_frequent_topics ---
def analyze_frequent_topics(user_history):
    topic_counts = defaultdict(int)
    for entry in user_history:
        question = entry.get('question', '').lower()
        for topic in algebra_topics.keys():
            if topic in question:
                topic_counts[topic] += 1
    sorted_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)
    return [topic for topic, count in sorted_topics[:2]]


# --- Function: get_predefined_hobby_answer ---
def get_predefined_hobby_answer(hobby, question_normalized, profile):
    """
    Attempt to generate a hobby-specific answer.
    
    This implementation checks a predefined dictionary of hobby responses.
    If the hobby is found and a keyword from that hobby is present in the question,
    a tailored response is returned.
    
    Args:
        hobby (str): The user's hobby (in lowercase).
        question_normalized (str): The normalized question text.
        profile (dict): The user's profile.
    
    Returns:
        str or None: A hobby-specific answer if available; otherwise, None.
    """
    # Define a simple mapping from hobbies to specialized responses.
    hobby_responses = {
        "soccer": "As a soccer enthusiast, {name}, you know that strategy and teamwork are key – apply the same approach to solving problems!",
        "reading": "Since you love reading, {name}, every problem can be seen as a story waiting to be explored.",
        "painting": "With your passion for painting, {name}, think of each problem as a blank canvas ready for creative solutions.",
        "hiking": "Hiking teaches perseverance, {name} – each step you take brings you closer to the solution."
    }
    if hobby in hobby_responses:
        # Check if any keyword from the hobby appears in the question.
        # (For simplicity, we assume if the hobby name is mentioned or the question is related, we return the answer.)
        if hobby in question_normalized:
            return hobby_responses[hobby].format(name=profile.get("name", "User"))
    return None