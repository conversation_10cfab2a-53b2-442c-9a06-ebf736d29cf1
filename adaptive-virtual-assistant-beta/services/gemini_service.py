# services/gemini_service.py

import google.generativeai as genai
from config import Config
import logging

genai.configure(api_key=Config.GEMINI_API_KEY)


# --- Function: generate_gemini_response ---
def generate_gemini_response(question, context=""):
    """
    Generate response using Gemini API with optional context
    """
    # Begin try block
    try:
        model = genai.GenerativeModel('gemini-pro')
        prompt = f"{context}\n\nQuestion: {question}\nAnswer:"
        response = model.generate_content(prompt)
        return response.text
    # Handle exception
    except Exception as e:
        logging.error(f"Gemini API Error: {str(e)}")
        return None