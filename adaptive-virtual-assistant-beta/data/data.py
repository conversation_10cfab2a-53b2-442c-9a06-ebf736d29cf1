# data/data.py

import json
import logging
import threading
import os

# A thread lock to prevent concurrent file access
lock = threading.Lock()

# Default file path that can be overridden via environment variable
PROFILES_FILE = os.environ.get('PROFILES_FILE_PATH', 'profiles.json')


# --- Function: load_profiles ---
def load_profiles(file_path=None):
    """
    Load user profiles from profiles.json.
    Returns:
        dict: Profiles keyed by username.
    """
    with lock:
        # Begin try block
        try:
            path = file_path or PROFILES_FILE   # grabs file path if passed or uses default from env or fallback
            with open(path, 'r') as f:
                profiles_list = json.load(f)
            profiles = {}
            for profile in profiles_list:
                profile.setdefault('histories', [])     # if histories key missing adds it as an empty list
                profiles[profile['username']] = profile
            logging.info("Profiles loaded successfully.")
            return profiles
        # Handle exception
        except FileNotFoundError:   # happens if the file just doesnt exist logs and returns empty dict 
            logging.error("profiles.json not found.")
            return {}
        # Handle exception
        except json.JSONDecodeError as e:   # file exists but is broken json
            logging.error(f"Error decoding profiles.json: {e}")
            return {}


# --- Function: save_profiles ---
def save_profiles(profiles, file_path=None):
    """
    Save profiles to profiles.json.
    Args:
        profiles (dict): Profiles keyed by username.
    """
    with lock:
        # Begin try block
        try:
            path = file_path or PROFILES_FILE   # same logic use passed path or default
            if os.path.exists(path):
                os.rename(path, path + '.bak')  # makes a backup of the existing file before overwriting
                
            with open(path, 'w') as f:
                json.dump(list(profiles.values()), f, indent=4)     # saves profiles as list of dicts with pretty printing
            logging.info("Profiles saved successfully.")
        # Handle exception
        except Exception as e:  # logs anything that might go wrong like file permissions
            logging.error(f"Error saving profiles.json: {e}")


# --- Function: update_profile ---
def update_profile(username, updated_data, file_path=None):
    """
    Update a specific user's profile.
    Args:
        username (str): The username.
        updated_data (dict): Data to update.
    """
    profiles = load_profiles(file_path)
    if username in profiles:
        profiles[username].update(updated_data)     # merge new data into the existing profile
        save_profiles(profiles, file_path)
    else:
        logging.warning(f"Attempted to update non-existent user '{username}'.")   # someone tried updating a user that doesnt exist in file