# generate_profiles.py

from werkzeug.security import generate_password_hash
import json


# --- Function: generate_profiles ---
def generate_profiles(output_file='profiles.json', users_input_file='users_input.json'):
    # creates demo user profiles with educational and personal fields
    # hashes raw passwords and saves the profiles into the given output file

    # Try to load users from external file first
    try:
        with open(users_input_file, 'r') as f:
            users = json.load(f)
            # loads list of users from external json file to allow easier updates
        print(f"Loaded users from {users_input_file}")
    except FileNotFoundError:
        print(f"No {users_input_file} found, using default hardcoded users")
        # fallback to hardcoded users if external file doesn't exist
        users = [
        {
            "username": "user1",
            "password": "password1",
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "phone_number": "************",
            "age": 5,
            "gender": "female",
            "academic_level": "Kindergarten",
            "preferred_subject": "Math",
            "learning_style": "Visual",
            "location": "USA",
            "language": "English",
            "timezone": "America/New_York",
            "cultural_background": "American",
            "hobbies": ["Drawing", "Playing with blocks"],
            "goals": ["Learn to read", "Make friends"],
            "strengths": ["Creativity", "Imagination"],
            "weaknesses": ["Short attention span"],
            "is_admin": False
        },
        # ... add additional users ...
    ]
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format in {users_input_file} - {e}")
        return
    except Exception as e:
        print(f"Error: Failed to load {users_input_file} - {e}")
        return

    profiles = []
    for i, user in enumerate(users, start=1):
        profile = {
            "user_id": i,
            "username": user["username"],
            "password_hash": generate_password_hash(user["password"]),  # hashes the plain text password using werkzeug defaults
            "name": user["name"],
            "email": user["email"],
            "phone_number": user["phone_number"],
            "age": user["age"],
            "gender": user["gender"],
            "academic_level": user["academic_level"],
            "preferred_subject": user["preferred_subject"],
            "learning_style": user["learning_style"],
            "location": user["location"],
            "language": user["language"],
            "timezone": user["timezone"],
            "cultural_background": user["cultural_background"],
            "hobbies": user["hobbies"],
            "goals": user["goals"],
            "strengths": user["strengths"],
            "weaknesses": user["weaknesses"],
            "is_admin": user["is_admin"],
            "histories": []
        }
        profiles.append(profile)

    try:
        with open(output_file, 'w') as f: # writes the full list of user profiles into json file with indent for readability
            json.dump(profiles, f, indent=4)
        print(f"{output_file} has been created with hashed passwords.")  # simple status print to confirm file was generated
    except PermissionError:
        print(f"Error: Permission denied when writing to {output_file}")
    except OSError as e:
        print(f"Error: failed to write profiles {output_file} due to error {e}")
    except Exception as e:
        print(f"Unexpected error writing {output_file}: {e}")

if __name__ == '__main__':
    generate_profiles()  # can now also call generate_profiles('test.json', 'custom_users.json') if needed
