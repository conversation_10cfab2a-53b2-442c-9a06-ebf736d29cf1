# forms.py

from flask_wtf import <PERSON>laskForm
from wtforms import (
    StringField, PasswordField, SubmitField, BooleanField,
    SelectField, IntegerField
)
from wtforms.fields import EmailField
from wtforms.validators import (
    DataRequired, Optional, Length, Email, EqualTo,
    NumberRange, Regexp
)

class RegistrationForm(FlaskForm):
    name = StringField('Full Name', validators=[
        DataRequired(message="Full name is required."),
        Length(min=2, max=100)
    ])
    
    username = StringField('Username', validators=[
        DataRequired(message="Username is required."),
        Length(min=4, max=25),
        Regexp(r'^\w+$', message="Username may only contain letters, numbers, and underscores.")
    ])

    email = StringField('Email', validators=[
        DataRequired(message="Email is required."),
        Email(message="Invalid email format.")
    ])

    phone_number = StringField('Phone Number', validators=[
        Optional(),
        Regexp(r'^\+?[\d\s\-()]{7,20}$', message="Enter a valid phone number.")
    ])

    age = IntegerField('Age', validators=[
        DataRequired(message="Age is required."),
        NumberRange(min=1, max=120, message="Age must be between 1 and 120.")
    ])

    gender = SelectField('Gender', choices=[
        ('female', 'Female'),
        ('male', 'Male'),
        ('other', 'Other'),
        ('prefer_not_to_say', 'Prefer not to say')
    ], validators=[DataRequired(message="Please select a gender.")])

    academic_level = StringField('Academic Level', validators=[
        DataRequired(message="Academic level is required."),
        Length(max=50)
    ])

    preferred_subject = StringField('Preferred Subject', validators=[
        DataRequired(message="Preferred subject is required.")
    ])

    learning_style = SelectField('Learning Style', choices=[
        ('Visual', 'Visual'),
        ('Auditory', 'Auditory'),
        ('Kinesthetic', 'Kinesthetic')
    ], validators=[DataRequired(message="Please select a learning style.")])

    location = StringField('Location', validators=[
        Optional(), Length(max=100)
    ])

    language = StringField('Language', validators=[
        Optional(), Length(max=50)
    ])

    timezone = StringField('Timezone', validators=[
        Optional(), Length(max=100)
    ])

    hobbies = StringField('Hobbies', validators=[Optional()])
    goals = StringField('Goals', validators=[Optional()])
    strengths = StringField('Strengths', validators=[Optional()])
    weaknesses = StringField('Weaknesses', validators=[Optional()])

    password = PasswordField('Password', validators=[
        DataRequired(message="Password is required."),
        Length(min=6, message="Password must be at least 6 characters.")
    ])

    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(message="Please confirm your password."),
        EqualTo('password', message="Passwords must match.")
    ])

    submit = SubmitField('Register')

class LoginForm(FlaskForm):
    """Form for user login."""
    username = StringField('Username', validators=[
        DataRequired(message="Username is required.")
    ])
    
    password = PasswordField('Password', validators=[
        DataRequired(message="Password is required.")
    ])
    
    remember = BooleanField('Remember Me')
    
    submit = SubmitField('Login')

class UpdatePreferencesForm(FlaskForm):
    """Form for updating user preferences."""
    learning_style = SelectField('Learning Style', choices=[
        ('Visual', 'Visual'),
        ('Auditory', 'Auditory'),
        ('Kinesthetic', 'Kinesthetic')
    ], validators=[DataRequired(message="Learning style is required.")])
    
    preferred_subject = StringField('Preferred Subject', validators=[
        DataRequired(message="Preferred subject is required.")
    ])
    
    goals = StringField('Goals', validators=[Optional()])
    hobbies = StringField('Hobbies', validators=[Optional()])
    strengths = StringField('Strengths', validators=[Optional()])
    weaknesses = StringField('Weaknesses', validators=[Optional()])
    
    # Additional fields that might be useful
    email = EmailField('Email', validators=[Optional(), Email()])
    language = StringField('Language', validators=[Optional()])
    cultural_background = StringField('Cultural Background', validators=[Optional()])
    
    submit = SubmitField('Update Preferences')
