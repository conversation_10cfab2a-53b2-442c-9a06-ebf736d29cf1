# models/user.py

from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

class User(UserMixin):

# --- Function: __init__ ---
    def __init__(self, username, is_admin=False):
        self.id = username  # <PERSON><PERSON><PERSON>-<PERSON><PERSON> uses 'id' as the unique identifier
        self.username = username
        self.is_admin = is_admin
        self.password_hash = None
        self.name = None  
        self.user_id = None 


# --- Function: set_password ---
    def set_password(self, password):
        # Remove the explicit method parameter so that the default is used
        self.password_hash = generate_password_hash(password)


# --- Function: verify_password ---
    def verify_password(self, password):
        if not self.password_hash:
            return False
        return check_password_hash(self.password_hash, password)

    @staticmethod

# --- Function: generate_password_hash_static ---
    def generate_password_hash_static(password):
        return generate_password_hash(password)
    

# --- Function: get_id ---
    def get_id(self):
        return self.username