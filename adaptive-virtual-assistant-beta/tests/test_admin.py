import unittest
import os
import json
import tempfile
from unittest.mock import patch
from flask_login import login_user

from app import create_app
from models.user import User
from data.data import load_profiles

# Define test data file path
TEST_PROFILES_FILE = 'test_profiles.json'

"""
Tests for the admin.py routes.
"""
class TestAdmin(unittest.TestCase):

    def setUp(self):
        """
        Set up test environment before each test.
        """
        # Create test Flask app with test config
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['WTF_CSRF_ENABLED'] = False
        self.app.config['SERVER_NAME'] = 'localhost.localdomain'  # Required for url_for to work
        self.client = self.app.test_client()
        
        # Set up application context
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Create a temp directory for test files
        self.test_dir = tempfile.mkdtemp()
        self.test_profiles_file = os.path.join(self.test_dir, 'test_profiles.json')
        
        # Create test profiles data
        self.profiles = {}
        
        # Add admin user to profiles
        self.profiles['testadmin'] = {
            'username': 'testadmin',
            'is_admin': True,
            'user_id': 999,
            'name': 'Test Admin',
            'email': '<EMAIL>',
            'phone_number': '555-1234',
            'age': 30,
            'gender': 'Male',
            'academic_level': 'Graduate',
            'preferred_subject': 'mathematics',
            'learning_style': 'visual',
            'location': 'Phoenix',
            'language': 'english',
            'timezone': 'America/Phoenix',
            'cultural_background': 'american',
            'hobbies': ['coding', 'reading'],
            'goals': ['learn programming'],
            'strengths': ['problem solving'],
            'weaknesses': ['time management'],
            'answer_type_weights': {
                "informational": 0.5,
                "real_world": 0.5,
                "cause_and_effect": 0.5,
                "goal_based": 0.5
            },
            'weight_adjustment_count': 0
        }
        
        # Add regular user to profiles
        self.profiles['regularuser'] = {
            'username': 'regularuser',
            'is_admin': False,
            'user_id': 998,
            'name': 'Regular User',
            'email': '<EMAIL>',
            'phone_number': '555-5678',
            'age': 25,
            'gender': 'Female',
            'academic_level': 'Undergraduate',
            'preferred_subject': 'science',
            'learning_style': 'auditory',
            'location': 'New York',
            'language': 'spanish',
            'timezone': 'America/New_York',
            'cultural_background': 'hispanic',
            'hobbies': ['music', 'sports'],
            'goals': ['graduate college'],
            'strengths': ['communication'],
            'weaknesses': ['procrastination'],
            'answer_type_weights': {
                "informational": 0.5,
                "real_world": 0.5,
                "cause_and_effect": 0.5,
                "goal_based": 0.5
            },
            'weight_adjustment_count': 0
        }
        
        # Save test profiles to the temp file
        with open(self.test_profiles_file, 'w') as f:
            json.dump(list(self.profiles.values()), f, indent=4)
        
        # Patch the PROFILES_FILE constant to use our test file
        self.patcher = patch('data.data.PROFILES_FILE', self.test_profiles_file)
        self.patcher.start()
        
        # Create User objects for login tests
        self.admin_user = User(username="testadmin", is_admin=True)
        self.admin_user.set_password("admin123")
        
        self.regular_user = User(username="regularuser", is_admin=False)
        self.regular_user.set_password("user123")
        
        # Define test login route
        @self.app.route('/test_login/<username>', methods=['GET'])
        def test_login(username):
            if username == "testadmin":
                login_user(self.admin_user)
            else:
                login_user(self.regular_user)
            return 'Logged in as ' + username

    def tearDown(self):
        """
        Clean up after each test.
        """
        self.patcher.stop()
        
        if os.path.exists(self.test_profiles_file):
            os.remove(self.test_profiles_file)
            
        os.rmdir(self.test_dir)
            
        self.app_context.pop()

    def login(self, user):
        """Helper method to simulate login with Flask-Login"""
        with self.client as c:
            with c.session_transaction() as sess:
                sess['_user_id'] = user.username
            c.get(f'/test_login/{user.username}')
    
    def test_admin_dashboard_access(self):
        """
        Test admin dashboard accessibility.
        """
        # Test with admin user
        self.login(self.admin_user)
        
        response = self.client.get('/admin/dashboard')
        self.assertEqual(response.status_code, 200)  # Expected: 200 OK for admin
        
        # Test with regular user
        self.login(self.regular_user)
        
        response = self.client.get('/admin/dashboard')
        self.assertEqual(response.status_code, 302)  # Expected: 302 Redirect for non-admin
        self.assertTrue(response.location == '/' or 'main.index' in response.location or '/main/index' in response.location)

    def test_view_user(self):
        """
        Test viewing a specific user's profile.
        """
        self.login(self.admin_user)
        
        response = self.client.get('/admin/user/regularuser')
        self.assertEqual(response.status_code, 200)  # Expected: 200 Profile found
        
        response = self.client.get('/admin/user/nonexistentuser')
        self.assertEqual(response.status_code, 302)  # Expected: 302 Redirect to dashboard
        self.assertIn('/admin/dashboard', response.location)

    def test_edit_user(self):
        """
        Test editing a user's profile.
        """
        self.login(self.admin_user)
        
        response = self.client.get('/admin/user/regularuser/edit')
        self.assertEqual(response.status_code, 200)  # Expected: 200 Form displayed
        
        updated_data = {
            'name': 'Updated Name',
            'email': '<EMAIL>',
            'is_admin': 'on'  # Make user an admin
        }
        
        response = self.client.post('/admin/user/regularuser/edit', data=updated_data)
        self.assertEqual(response.status_code, 302)  # Expected: 302 Redirect

        # Verify data was updated
        profiles = load_profiles()
        self.assertEqual(profiles['regularuser']['name'], 'Updated Name')
        self.assertEqual(profiles['regularuser']['email'], '<EMAIL>')
        self.assertTrue(profiles['regularuser']['is_admin'])


    def test_user_access_control(self):
        """
        Test access control for user profile viewing (non-admin shouldn't access).
        """
        # Test with regular user
        self.login(self.regular_user)

        response = self.client.get('/admin/user/testadmin')
        self.assertEqual(response.status_code, 302)  # Expected: 302 Redirect for non-admin
        # Check that it redirects to main page (actual implementation)
        self.assertEqual('/', response.location)
    
    def test_edit_user_access_control(self):
        """
        Test access control for user editing (non-admin shouldn't edit).
        """
        self.login(self.regular_user)
        
        response = self.client.get('/admin/user/testadmin/edit')
        self.assertEqual(response.status_code, 302)  # Expected: 302 Redirect for non-admin
        
        self.assertTrue('/' in response.location or '/admin/dashboard' in response.location)
        
        updated_data = {
            'name': 'Hacked Name',
            'email': '<EMAIL>',
        }
        
        response = self.client.post('/admin/user/testadmin/edit', data=updated_data)
        self.assertEqual(response.status_code, 302)  # Expected: 302 Redirect
        
        profiles = load_profiles()
        self.assertNotEqual(profiles['testadmin']['name'], 'Hacked Name')
        self.assertNotEqual(profiles['testadmin']['email'], '<EMAIL>')


if __name__ == '__main__':
    unittest.main()