import pytest
import os
import json
import tempfile
from unittest.mock import patch
from flask import url_for
from app import create_app
from data.data import load_profiles, save_profiles

@pytest.fixture
def app():
    app = create_app()
    app.config.update({
        "TESTING": True,
        "LOGIN_DISABLED": False,
        "WTF_CSRF_ENABLED": False,
        "KEYWORD_SYNONYMS": {"math": ["math", "arithmetic", "numbers"]}
    })
    yield app

@pytest.fixture
def client(app):
    return app.test_client()

@pytest.fixture
def authenticated_client(client, app):
    # Create a temp directory & file for test profiles
    test_dir = tempfile.mkdtemp()
    test_profiles_file = os.path.join(test_dir, 'test_main_profiles.json')
    
    # Create test user data
    test_user = {
        "user_id": 1,
        "username": "user1",
        "email": "<EMAIL>",
        "password_hash": "test",
        "name": "Test User",
        "phone_number": "************",
        "age": 10,
        "gender": "female",
        "academic_level": "Elementary",
        "preferred_subject": "Math",
        "learning_style": "Visual",
        "location": "USA",
        "language": "English",
        "timezone": "America/New_York",
        "cultural_background": "American",
        "hobbies": ["drawing"],
        "goals": ["learn math"],
        "strengths": ["creativity"],
        "weaknesses": ["attention span"],
        "is_admin": False,
        "histories": [],
        "xp": 100,
        "level": 1,
        "streak": 3,
        "badges": [],
        "answer_type_weights": {
            "informational": 0.5,
            "real_world": 0.5,
            "cause_and_effect": 0.5,
            "goal_based": 0.5
        },
        "weight_adjustment_count": 0
    }
    
    # Save test profiles to the temp file
    with open(test_profiles_file, 'w') as f:
        json.dump([test_user], f, indent=4)
    
    # Patch the PROFILES_FILE constant to use our test file
    with patch('data.data.PROFILES_FILE', test_profiles_file):
        # Login the test user
        with client.session_transaction() as session:
            session["_user_id"] = "user1"
        
        yield client
    
    # Clean up after test
    if os.path.exists(test_profiles_file):
        os.remove(test_profiles_file)
    os.rmdir(test_dir)

def test_index_page(authenticated_client):
    response = authenticated_client.get("/")
    assert response.status_code == 200
    assert b"What is math?" in response.data

def test_about_page(client):
    response = client.get("/about")
    assert response.status_code == 200

def test_profile_view(authenticated_client):
    response = authenticated_client.get("/profile")
    assert response.status_code == 200
    assert b"Profile" in response.data  # something that will definitely be on the page

def test_get_answer_invalid(authenticated_client):
    response = authenticated_client.post("/get_answer", json={})
    assert response.status_code == 400

def test_get_answer_valid(authenticated_client, monkeypatch):
    def mock_get_ai_answer(q, profile, synonyms): 
        return "This is a [Mock] style answer to test"
    
    monkeypatch.setattr("services.answer_service.get_ai_answer", mock_get_ai_answer)
    response = authenticated_client.post("/get_answer", json={"question": "What is math?"})
    assert response.status_code == 200
    
    # Check for partial match in response instead of exact string
    response_data = response.data.decode('utf-8')
    assert "style answer" in response_data

def test_history_view(authenticated_client):
    response = authenticated_client.get("/history")
    assert response.status_code == 200

def test_clear_history(authenticated_client):
    response = authenticated_client.post("/clear_history")
    assert response.status_code == 302  # Expect redirect

def test_ask_question_get(authenticated_client):
    response = authenticated_client.get("/ask")
    assert response.status_code in [200, 302]

def test_leaderboard(authenticated_client):
    response = authenticated_client.get("/leaderboard")
    assert response.status_code == 200
    assert b"Leaderboard" in response.data
    response = authenticated_client.get("/leaderboard", follow_redirects=True)
    assert b"user1" in response.data or b"User" in response.data
