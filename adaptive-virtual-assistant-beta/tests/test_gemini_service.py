import pytest
from unittest.mock import patch, MagicMock
from services import gemini_service


def test_generate_gemini_response_success():
    mock_response = MagicMock()
    mock_response.text = "This is a test response."

    with patch("services.gemini_service.genai.GenerativeModel") as MockModel:
        mock_model_instance = MockModel.return_value
        mock_model_instance.generate_content.return_value = mock_response

        question = "What is AI?"
        context = "This is the context."
        result = gemini_service.generate_gemini_response(question, context)

        expected_prompt = f"{context}\n\nQuestion: {question}\nAnswer:"
        mock_model_instance.generate_content.assert_called_once_with(expected_prompt)
        assert result == "This is a test response."


def test_generate_gemini_response_failure(caplog):
    with patch("services.gemini_service.genai.GenerativeModel") as MockModel:
        mock_model_instance = MockModel.return_value
        mock_model_instance.generate_content.side_effect = Exception("Mock API error")

        with caplog.at_level("ERROR"):
            result = gemini_service.generate_gemini_response("What is AI?")
            assert result is None
            assert "Gemini API Error: Mock API error" in caplog.text
