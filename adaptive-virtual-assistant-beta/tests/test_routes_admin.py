import pytest
from flask import url_for
from unittest.mock import patch, MagicMock
from app import create_app
from models.user import User


@pytest.fixture
def client():
    app = create_app()
    app.config['TESTING'] = True
    app.secret_key = 'test_secret'

    with app.test_client() as client:
        with app.app_context():
            yield client


@pytest.fixture
def admin_user():
    return User(username='admin', is_admin=True)


@pytest.fixture
def basic_user():
    return User(username='basic', is_admin=False)


@patch('flask_login.utils._get_user')
def login_user(client, user, mock_get_user):
    """Properly simulate login for testing"""
    mock_get_user.return_value = user
    with client.session_transaction() as sess:
        sess['_user_id'] = user.username
        sess['_fresh'] = True


@patch('routes.admin.current_user')
@patch('routes.admin.load_profiles')
@patch('routes.admin.render_template')
def test_admin_dashboard_access_granted(mock_render, mock_load_profiles, mock_current_user, client, admin_user):
    # Set up mocks
    mock_profiles = {'admin': {'username': 'admin', 'is_admin': True}}
    mock_load_profiles.return_value = mock_profiles
    
    mock_current_user.is_authenticated = True
    mock_current_user.is_admin = True
    mock_current_user.username = 'admin'
    mock_current_user.get_id.return_value = 'admin'
    
    with client.session_transaction() as sess:
        sess['_user_id'] = 'admin'
        sess['_fresh'] = True
    
    res = client.get('/admin/dashboard')
    
    assert res.status_code == 200
    mock_render.assert_called_once_with('admin_dashboard.html', users=mock_profiles, current_user=mock_current_user)


@patch('routes.admin.load_profiles')
def test_admin_dashboard_access_denied(mock_load_profiles, client, basic_user):
    mock_load_profiles.return_value = {}
    login_user(client, basic_user)
    
    res = client.get('/admin/dashboard', follow_redirects=False)
    
    assert res.status_code == 302
    assert '/main/index' in res.location or '/' in res.location


@patch('routes.admin.render_template')
@patch('routes.admin.load_profiles')
def test_view_user_found(mock_load_profiles, mock_render, client, admin_user):
    # Set up mocks
    mock_profiles = {'testuser': {'username': 'testuser', 'is_admin': False}}
    mock_load_profiles.return_value = mock_profiles
    mock_render.return_value = "User Profile View Mock"
    
    login_user(client, admin_user)
    res = client.get('/admin/user/testuser')
    
    assert res.status_code == 200
    mock_render.assert_called_once_with('view_user.html', profile=mock_profiles['testuser'])


@patch('routes.admin.load_profiles')
def test_view_user_not_found(mock_load_profiles, client, admin_user):
    mock_load_profiles.return_value = {}
    login_user(client, admin_user)
    res = client.get('/admin/user/missinguser', follow_redirects=True)
    assert b'User not found.' in res.data


@patch('routes.admin.save_profiles')
@patch('routes.admin.load_profiles')
def test_edit_user_post_updates_profile(mock_load_profiles, mock_save_profiles, client, admin_user):
    mock_profiles = {
        'testuser': {
            'username': 'testuser',
            'name': '',
            'email': '',
            'hobbies': [],
            'goals': [],
            'strengths': [],
            'weaknesses': [],
            'is_admin': False
        }
    }
    mock_load_profiles.return_value = mock_profiles
    login_user(client, admin_user)

    form_data = {
        'name': 'Updated Name',
        'email': '<EMAIL>',
        'hobbies': 'Reading, Coding',
        'goals': 'Learn Python',
        'strengths': 'Persistence',
        'weaknesses': 'Impatience',
        'is_admin': 'on'
    }

    res = client.post('/admin/user/testuser/edit', data=form_data, follow_redirects=True)
    assert b'User profile updated successfully.' in res.data
    assert mock_profiles['testuser']['name'] == 'Updated Name'
    assert mock_profiles['testuser']['email'] == '<EMAIL>'
    assert mock_profiles['testuser']['is_admin'] is True
    assert mock_profiles['testuser']['hobbies'] == ['Reading', 'Coding']


@patch('routes.admin.load_profiles')
def test_edit_user_get_renders_page(mock_load_profiles, client, admin_user):
    mock_profiles = {
        'testuser': {
            'username': 'testuser',
            'name': 'Test Name',
            'email': '<EMAIL>',
            'hobbies': [],
            'goals': [],
            'strengths': [],
            'weaknesses': [],
            'is_admin': False
        }
    }
    mock_load_profiles.return_value = mock_profiles
    login_user(client, admin_user)
    res = client.get('/admin/user/testuser/edit')
    assert b'Edit User Profile' in res.data


@patch('routes.admin.save_profiles')
@patch('routes.admin.load_profiles')
def test_edit_user_with_empty_fields(mock_load_profiles, mock_save_profiles, client, admin_user):
    """Test editing a user with empty optional fields preserves existing values."""
    mock_profiles = {
        'testuser': {
            'username': 'testuser',
            'name': 'Original Name',
            'email': '<EMAIL>',
            'hobbies': ['Original Hobby'],
            'is_admin': False
        }
    }
    mock_load_profiles.return_value = mock_profiles
    login_user(client, admin_user)

    # Only update name, leave other fields empty
    form_data = {
        'name': 'New Name',
        'email': '',
        'hobbies': '',
    }

    res = client.post('/admin/user/testuser/edit', data=form_data, follow_redirects=True)
    
    assert b'User profile updated successfully' in res.data
    assert mock_profiles['testuser']['name'] == 'New Name'
    assert mock_profiles['testuser']['email'] == ''
    assert mock_profiles['testuser']['hobbies'] == []