import unittest
import os
import json
import tempfile
from unittest.mock import patch

from app import create_app
from models.user import User

# Define test data file path
TEST_PROFILES_FILE = 'test_auth_profiles.json'

"""
Tests for the auth.py routes.
"""
class TestAuth(unittest.TestCase):

    def setUp(self):
        """
        Set up test environment before each test.
        """
        # Create test Flask app with test config
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['WTF_CSRF_ENABLED'] = False
        self.app.config['SERVER_NAME'] = 'localhost.localdomain'
        self.client = self.app.test_client()
        
        # Set up application context
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Create test profiles data
        self.profiles = {}
        
        # Add test user to profiles
        self.username = "testuser"
        self.password = "testpassword123"
        
        test_user = User(username=self.username)
        test_user.set_password(self.password)
        
        self.profiles[self.username] = {
            'username': self.username,
            'password_hash': test_user.password_hash,
            'is_admin': False,
            'user_id': 1,
            'name': 'Test User',
            'email': '<EMAIL>',
            'phone_number': '555-1234',
            'histories': []
        }
        
        # Save test profiles to separate test file
        self.test_dir = tempfile.mkdtemp()
        self.test_profiles_file = os.path.join(self.test_dir, 'test_auth_profiles.json')
        # Save test profiles to the temp file
        with open(self.test_profiles_file, 'w') as f:
            json.dump(list(self.profiles.values()), f, indent=4)
        # Patch the PROFILES_FILE constant
        self.patcher = patch('data.data.PROFILES_FILE', self.test_profiles_file)
        self.patcher.start()

    def tearDown(self):
        """
        Clean up after each test.
        """
        self.patcher.stop()
        
        if os.path.exists(self.test_profiles_file):
            os.remove(self.test_profiles_file)
        if os.path.exists(self.test_dir):
            os.rmdir(self.test_dir)
            
        self.app_context.pop()

    def test_login_page(self):
        """
        Test login page loads correctly.
        """
        response = self.client.get('/auth/login')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'login', response.data.lower())

    def test_successful_login(self):
        """
        Test user can log in with correct credentials.
        """
        response = self.client.post('/auth/login', data={
            'username': self.username,
            'password': self.password
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'logged in successfully', response.data.lower())

    def test_failed_login_wrong_password(self):
        """
        Test login fails with incorrect password.
        """
        response = self.client.post('/auth/login', data={
            'username': self.username,
            'password': 'wrongpassword'
        })
        
        self.assertEqual(response.status_code, 200)  # Returns login page with error
        self.assertIn(b'invalid username or password', response.data.lower())
        
    def test_failed_login_nonexistent_user(self):
        """
        Test login fails with non-existent user.
        """
        response = self.client.post('/auth/login', data={
            'username': 'nonexistentuser',
            'password': 'somepassword'
        })
        
        self.assertEqual(response.status_code, 200)  # Returns login page with error
        self.assertIn(b'invalid username or password', response.data.lower())
    
    def test_logout(self):
        """
        Test user can successfully log out.
        """
        # First login
        self.client.post('/auth/login', data={
            'username': self.username,
            'password': self.password
        }, follow_redirects=True)
        
        # Then logout
        response = self.client.get('/auth/logout', follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'logged out', response.data.lower())

    def test_registration_functionality(self):
        """
        Test user registration functionality.
        """
        # Test successful registration
        response = self.client.post('/auth/register', data={
            'username': 'newuser',
            'password': 'password123',
            'confirm_password': 'password123'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'registration successful', response.data.lower())
        
        # Test registration with existing username
        response = self.client.post('/auth/register', data={
            'username': self.username,  # Using existing username from setUp
            'password': 'password123',
            'confirm_password': 'password123'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'username already exists', response.data.lower())
        
        # Test registration with mismatched passwords
        response = self.client.post('/auth/register', data={
            'username': 'anotheruser',
            'password': 'password123',
            'confirm_password': 'differentpassword'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'passwords do not match', response.data.lower())
        
        # Test registration with missing fields
        response = self.client.post('/auth/register', data={
            'username': 'anotheruser',
            'password': '',
            'confirm_password': ''
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'please fill out all fields', response.data.lower())


if __name__ == '__main__':
    unittest.main()