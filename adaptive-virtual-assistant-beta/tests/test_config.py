import os
import importlib
import pytest

@pytest.fixture(autouse=True)
def reset_env(monkeypatch):
    # Ensure environment is clean before each test
    keys_to_clear = ['SECRET_KEY', 'GEMINI_API_KEY']
    for key in keys_to_clear:
        monkeypatch.delenv(key, raising=False)


def test_config_defaults(monkeypatch):
    monkeypatch.delenv('GEMINI_API_KEY', raising=False)
    
    # Force reload the config module to ensure clean slate
    import config
    import importlib
    importlib.reload(config)
    
    from config import Config

    assert Config.SECRET_KEY == 'aSyAyl2yCFgeu5sIYegbe75iYs26l88aM0qI'
    
    assert hasattr(Config, 'GEMINI_API_KEY')
    
    assert isinstance(Config.KEYWORDS, list)
    assert 'equation' in Config.KEYWORDS


def test_config_env_overrides(monkeypatch):
    monkeypatch.setenv('SECRET_KEY', 'supersecret')
    monkeypatch.setenv('GEMINI_API_KEY', 'test-gemini-key')

    # Reload the config module to apply monkeypatched env
    import config
    importlib.reload(config)

    assert config.Config.SECRET_KEY == 'supersecret'
    assert config.Config.GEMINI_API_KEY == 'test-gemini-key'
    assert 'solve' in config.Config.KEYWORDS
