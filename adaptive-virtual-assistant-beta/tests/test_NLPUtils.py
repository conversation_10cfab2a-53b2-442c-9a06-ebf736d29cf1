import unittest
import nltk
from utils.nlp_utils import (
    preprocess_input,
    get_synonyms,
    match_keywords,
    build_keyword_synonym_dict,
    process_question
)

class TestNLPUtils(unittest.TestCase):
    """
    Tests for the nlp_utils.py module.
    """
    
    def setUp(self):
        """
        Set up test data before each test.
        """
        self.sample_text = "What is algebra and how does it work?"
        self.sample_keywords = ["algebra", "math", "equation"]
        
    def test_get_synonyms(self):
        """
        Test get_synonyms returns appropriate synonyms for a word.
        """
        synonyms = get_synonyms("happy")
        
        # Check that synonyms are returned
        self.assertTrue(len(synonyms) > 0)  # Expected: True, 'happy' has synonyms
        self.assertTrue(all(isinstance(syn, str) for syn in synonyms))  # Expected: True, all synonyms are strings
        
    def test_match_keywords(self):
        """
        Test match_keywords correctly matches tokens to keywords.
        """
        # Create keyword synonyms dict
        keyword_synonyms = {
            "algebra": ["algebra", "algebraic"],
            "math": ["math", "mathematics", "mathematical"],
            "equation": ["equation", "formula", "expression"]
        }
        
        # Test with tokens that match
        tokens = ["algebra", "mathematics"]
        matched = match_keywords(tokens, keyword_synonyms)
        
        self.assertEqual(len(matched), 2)  # Expected: 2 keywords matched
        self.assertIn("algebra", matched)  # Expected: True, 'algebra' matched
        self.assertIn("math", matched)  # Expected: True, 'math' matched via 'mathematics' synonym
        self.assertNotIn("equation", matched)  # Expected: True, 'equation' not matched
        
        # Test with tokens that don't match
        tokens = ["history", "literature"]
        matched = match_keywords(tokens, keyword_synonyms)
        
        self.assertEqual(len(matched), 0)  # Expected: 0 keywords matched