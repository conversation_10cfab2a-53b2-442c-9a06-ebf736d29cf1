# File: tests/test_user.py
import unittest
from models.user import User
from werkzeug.security import check_password_hash

"""
Tests for the user.py.
"""
class TestUser(unittest.TestCase):

    def setUp(self):
        """
        Set up mock person for testing.
        """
        self.username = "testuser"
        self.password = "testpassword123"
        self.user = User(username=self.username)


    def test_user_initialization(self):
        """
        Test user init with different parameters.
        """
        # Test default init
        self.assertEqual(self.user.username, self.username)  # Expected: True, testuser == testuser
        self.assertEqual(self.user.id, self.username)  # Expected: True, testuser == testuser
        self.assertFalse(self.user.is_admin)  # Expected: False
        self.assertIsNone(self.user.password_hash)  # Expected: None, not initialized
        self.assertIsNone(self.user.name)  # Expected: None, not initialized
        self.assertIsNone(self.user.user_id)  # Expected: None, not initialized

        # Test admin init
        admin_user = User(username="admin", is_admin=True)
        self.assertEqual(admin_user.username, "admin")  # Expected: True, admin == admin
        self.assertEqual(admin_user.id, "admin") # Expected: True, admin == admin
        self.assertTrue(admin_user.is_admin)  # Expected: True
        self.assertIsNone(admin_user.password_hash)  # Expected: None, not initialized
        self.assertIsNone(admin_user.name) # Expected: None, not initialized
        self.assertIsNone(admin_user.user_id) # Expected: None, not initialized


    def test_set_password(self):
        """
        Test setting the user's password creates a valid hash.
        """
        # Initial password
        self.assertIsNone(self.user.password_hash)  # Expected: None, not initialized
        
        # Set password
        self.user.set_password(self.password)
        
        # Password hash should change to a non-empty string
        self.assertIsNotNone(self.user.password_hash)  # Expected: not None, password was set
        self.assertIsInstance(self.user.password_hash, str)  # Expected: True, password_hash is a string
        self.assertTrue(len(self.user.password_hash) > 0)  # Expected: True, password_hash is empty


    def test_verify_password(self):
        """
        Test password verification works.
        """
        # Set password
        self.user.set_password(self.password)
        
        # Correct password
        self.assertTrue(self.user.verify_password(self.password))  # Expected: True, password matches hash
        
        # Incorrect password
        self.assertFalse(self.user.verify_password("wrongpassword"))  # Expected: False, password doesn't match
        
        # Test user with no password hash
        new_user = User(username="nopassword")
        self.assertFalse(new_user.verify_password(self.password))  # Expected: False, no such password hash


    def test_generate_password_hash_static(self):
        """
        Test static password hash generation creates valid scrypt hash.
        """
        password_hash = User.generate_password_hash_static(self.password)
        
        # Hash should be non-empty string
        self.assertIsNotNone(password_hash)  # Expected: not None, hash was set
        self.assertIsInstance(password_hash, str)  # Expected: True, hash is a string
        self.assertTrue(len(password_hash) > 0)  # Expected: True, hash is not empty
        
        # Hash format should match format used in the user
        self.assertTrue(password_hash.startswith("scrypt:"))  # Expected: True, hash starts with "scrypt:"
        
        # Verify hash works with password
        self.assertTrue(check_password_hash(password_hash, self.password))  # Expected: True, hash is correct password


    def test_get_id(self):
        """
        Test get_id method returns the username correctly.
        """
        # Get_id should return username for Flask-Login
        self.assertEqual(self.user.get_id(), self.username)  # Expected: True, testuser == testuser
        
        # Test with a different username
        other_user = User(username="otherrandomuser")
        self.assertEqual(other_user.get_id(), "otherrandomuser")  # Expected: True, otherrandomuser == otherrandomuser

    def test_update_password(self):
            """
            Test that updating the password changes the password hash,
            so that the old password no longer verifies and the new one does.
            """
            # Set the initial password.
            self.user.set_password("initial123")
            self.assertTrue(self.user.verify_password("initial123"))
            
            # Update to a new password.
            self.user.set_password("newpassword456")
            self.assertFalse(self.user.verify_password("initial123"))
            self.assertTrue(self.user.verify_password("newpassword456"))
            
if __name__ == '__main__':
    unittest.main()