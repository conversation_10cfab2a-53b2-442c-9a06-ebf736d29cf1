import unittest
import os
import json
import tempfile
from unittest.mock import patch, MagicMock
from flask import url_for

from app import create_app
from models.user import User
from services import answer_service


TEST_PROFILES_FILE = 'test_main_profiles.json'

class TestMain(unittest.TestCase):

    def setUp(self):
        """
        Set up test environment before each test.
        """
        # Create test Flask app with test config
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['WTF_CSRF_ENABLED'] = False
        self.app.config['SERVER_NAME'] = 'localhost.localdomain'
        self.client = self.app.test_client()
        
        # Set up application context
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Create test profiles data
        self.username = "testuser"
        self.password = "testpassword123"
        
        test_user = User(username=self.username)
        test_user.set_password(self.password)
        
        self.profiles = {
            self.username: {
                'user_id': '1',
                'username': self.username,
                'password_hash': test_user.password_hash,
                'is_admin': False,
                'name': 'Test User',
                'email': '<EMAIL>',
                'learning_style': 'visual',
                'preferred_subject': 'math',
                'hobbies': ['reading', 'gaming'],
                'goals': ['learn algebra'],
                'strengths': ['focus'],
                'weaknesses': ['time management'],
                'histories': []
            }
        }
        
        # Save test profiles to separate test file
        self.test_dir = tempfile.mkdtemp()
        self.test_profiles_file = os.path.join(self.test_dir, 'test_main_profiles.json')
        # Save test profiles to the temp file
        with open(self.test_profiles_file, 'w') as f:
            json.dump(list(self.profiles.values()), f, indent=4)
        # Patch the PROFILES_FILE constant
        self.patcher = patch('data.data.PROFILES_FILE', self.test_profiles_file)
        self.patcher.start()
        
        # Login helper
        @self.app.route('/test_login/<username>', methods=['GET'])
        def test_login(username):
            user = User(username=username)
            self.login_user(user)
            return f'Logged in as {username}'

    def tearDown(self):
        """
        Clean up after each test.
        """
        self.patcher.stop()
        
        if os.path.exists(self.test_profiles_file):
            os.remove(self.test_profiles_file)
            
        os.rmdir(self.test_dir)
        
        self.app_context.pop()

    def login_user(self, user):
        """Helper method to simulate login with Flask-Login"""
        with self.client.session_transaction() as sess:
            sess['_user_id'] = user.username

    def test_index_requires_login(self):
        """
        Test that index redirects to login when not authenticated.
        """
        response = self.client.get('/', follow_redirects=False)
        self.assertEqual(response.status_code, 302)
        self.assertIn('/auth/login', response.location)

    def test_about_page(self):
        """
        Test the about page loads correctly.
        """
        # Login first
        self.client.get(f'/test_login/{self.username}')
        
        # Access about page
        response = self.client.get('/about')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'About', response.data)

if __name__ == '__main__':
    unittest.main()