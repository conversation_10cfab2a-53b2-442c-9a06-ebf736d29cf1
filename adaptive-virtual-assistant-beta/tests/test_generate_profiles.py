import os
import json
import unittest
from unittest.mock import patch, mock_open
from generate_profiles import generate_profiles

class TestGenerateProfiles(unittest.TestCase):
    
    @patch('json.dump')
    @patch('builtins.open', new_callable=mock_open)
    def test_generate_profiles_creates_file(self, mock_file, mock_json_dump):
        """Test that profiles are generated correctly."""
        generate_profiles()
        
        # Check that file was opened for writing
        mock_file.assert_called_with('profiles.json', 'w')
        
        # Check that json.dump was called (data was written)
        self.assertTrue(mock_json_dump.called)
        
        # Check format of first argument to json.dump (the profiles data)
        args, _ = mock_json_dump.call_args
        profiles = args[0]
        
        # Verify profiles structure
        self.assertIsInstance(profiles, list)
        self.assertGreater(len(profiles), 0)
        
        # Verify profile fields
        first_profile = profiles[0]
        self.assertIn('user_id', first_profile)
        self.assertIn('username', first_profile)
        self.assertIn('password_hash', first_profile)
        self.assertEqual(first_profile['username'], 'user1')

if __name__ == '__main__':
    unittest.main()