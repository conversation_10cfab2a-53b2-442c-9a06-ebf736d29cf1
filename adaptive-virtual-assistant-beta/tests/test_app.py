import unittest
import os
import json
import logging
import tempfile
from unittest.mock import patch

import pytest

from app import create_app
from models.user import User

# Path for temporary test profile data
TEST_PROFILES_FILE = 'test_app_profiles.json'

class TestApp(unittest.TestCase):

    def setUp(self):
        """Set up test environment before each test."""
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['WTF_CSRF_ENABLED'] = False
        self.app.config['SERVER_NAME'] = 'localhost.localdomain'
        self.client = self.app.test_client()

        self.app_context = self.app.app_context()
        self.app_context.push()

        self.username = "testuser"
        self.password = "testpassword123"
        self.admin_username = "testadmin"
        self.admin_password = "adminpass123"

        # Create test user and admin user
        test_user = User(username=self.username)
        test_user.set_password(self.password)

        admin_user = User(username=self.admin_username, is_admin=True)
        admin_user.set_password(self.admin_password)

        self.profiles = {
            self.username: {
                'username': self.username,
                'password_hash': test_user.password_hash,
                'is_admin': False,
                'user_id': 1,
                'name': 'Test User',
                'email': '<EMAIL>',
                'phone_number': '555-1234',
                'histories': []
            },
            self.admin_username: {
                'username': self.admin_username,
                'password_hash': admin_user.password_hash,
                'is_admin': True,
                'user_id': 2,
                'name': 'Admin User',
                'email': '<EMAIL>',
                'phone_number': '555-5678',
                'histories': []
            }
        }

        # Save fake profiles to test file
        self.test_dir = tempfile.mkdtemp()
        self.test_profiles_file = os.path.join(self.test_dir, 'test_app_profiles.json')
        # Save test profiles to the temp file
        with open(self.test_profiles_file, 'w') as f:
            json.dump(list(self.profiles.values()), f, indent=4)
        # Patch the PROFILES_FILE constant
        self.patcher = patch('data.data.PROFILES_FILE', self.test_profiles_file)
        self.patcher.start()

    def tearDown(self):
        """Clean up test environment."""
        self.patcher.stop()
        
        if os.path.exists(self.test_profiles_file):
            os.remove(self.test_profiles_file)
            
        os.rmdir(self.test_dir)
        
        self.app_context.pop()

    def test_create_app(self):
        """Ensure app is created and config is loaded."""
        self.assertIsNotNone(self.app)
        self.assertTrue(self.app.config['SECRET_KEY'])
        self.assertIn('KEYWORD_SYNONYMS', self.app.config)

    def test_homepage_access(self):
        """Test homepage access (should redirect to login if not logged in)."""
        response = self.client.get('/')
        self.assertIn(response.status_code, [200, 302])

    def test_404_page(self):
        """Ensure 404 page is returned on unknown route."""
        response = self.client.get('/nonexistent')
        self.assertEqual(response.status_code, 404)
        self.assertTrue(b"Page not found" in response.data or b"404" in response.data)

    def test_error_handlers_exist(self):
        """Check that all custom error handlers are registered."""
        handler_spec = self.app.error_handler_spec.get(None, {})
        for code in [401, 403, 404, 500, 503]:
            self.assertIn(code, handler_spec)

    def test_blueprints_registered(self):
        """Ensure all route blueprints are registered."""
        route_names = list(self.app.view_functions.keys())
        self.assertTrue(any(name.startswith('main.') for name in route_names))
        self.assertTrue(any(name.startswith('auth.') for name in route_names))
        self.assertTrue(any(name.startswith('admin.') for name in route_names))

    def test_keyword_synonyms_loaded(self):
        """Ensure synonym dictionary was built at startup."""
        self.assertIsInstance(self.app.config.get('KEYWORD_SYNONYMS'), dict)
        self.assertIn('equation', self.app.config['KEYWORD_SYNONYMS'])

# Optional: Pytest-compatible fixture if needed elsewhere
@pytest.fixture
def app():
    app = create_app()
    app.config.update({
        "TESTING": True,
        "WTF_CSRF_ENABLED": False,
        "LOGIN_DISABLED": True,
    })
    return app

@pytest.fixture
def client(app):
    return app.test_client()
