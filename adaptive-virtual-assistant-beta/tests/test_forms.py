import pytest
from flask import Flask
from flask_wtf.csrf import CSRFProtect
from unittest.mock import patch

from wtforms import String<PERSON>ield

# Import forms with patch to avoid email validation error
with patch('wtforms.fields.EmailField', lambda *args, **kwargs: None):
    from forms import RegistrationForm, LoginForm, UpdatePreferencesForm

@pytest.fixture
def app():
    app = Flask(__name__)
    app.config['WTF_CSRF_ENABLED'] = False  # Disable CSRF for testing
    app.config['SECRET_KEY'] = 'test'
    CSRFProtect(app)
    return app

@pytest.fixture(autouse=True)
def mock_email_validator():
    """Mock email validator to bypass the package dependency"""
    with patch('wtforms.validators.Email', lambda message=None: lambda form, field: None):
        with patch('wtforms.validators.email', lambda: None):
            with patch('wtforms.fields.EmailField', lambda *args, **kwargs: StringField(*args, **kwargs)):
                yield


def test_registration_form_valid(app):
    with app.test_request_context():
        form = RegistrationForm(data={
            'username': 'testuser',
            'name': 'Test User',
            'email': '<EMAIL>',
            'age': 30,
            'gender': 'male',
            'academic_level': 'Graduate',
            'preferred_subject': 'Computer Science',
            'learning_style': 'Visual',
            'password': 'password123',
            'confirm_password': 'password123'
        })
        assert form.validate() is True


def test_registration_form_invalid_email(app):
    with app.test_request_context():
        form = RegistrationForm(data={
            'username': 'testuser',
            'name': 'Test User',
            'email': 'invalid-email',
            'age': 30,
            'gender': 'male',
            'academic_level': 'Graduate',
            'preferred_subject': 'Computer Science',
            'learning_style': 'Visual',
            'password': 'password123',
            'confirm_password': 'password123'
        })
        assert form.validate() is False
        assert 'Invalid email format.' in form.email.errors


def test_registration_form_password_mismatch(app):
    with app.test_request_context():
        form = RegistrationForm(data={
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'password123',
            'confirm_password': 'differentpass'
        })
        assert form.validate() is False
        assert 'Passwords must match.' in form.confirm_password.errors


def test_login_form_valid(app):
    with app.test_request_context():
        form = LoginForm(data={
            'username': 'testuser',
            'password': 'password123',
            'remember': True
        })
        assert form.validate() is True


def test_login_form_missing_fields(app):
    with app.test_request_context():
        form = LoginForm(data={
            'username': '',
            'password': ''
        })
        assert form.validate() is False
        assert 'Username is required.' in form.username.errors
        assert 'Password is required.' in form.password.errors


def test_update_preferences_form_missing_fields(app):
    with app.test_request_context():
        form = UpdatePreferencesForm(data={
            'learning_style': '',
            'preferred_subject': '',
            'goals': []
        })
        assert form.validate() is False
        assert 'Learning style is required.' in form.learning_style.errors
        assert 'Preferred subject is required.' in form.preferred_subject.errors
