{% extends "base.html" %}

{% block title %}Edit User - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <h2 class="mb-4 text-center">Edit User Profile</h2>
    
    <form method="POST" action="{{ url_for('admin.edit_user', username=profile.username) }}" aria-label="Edit User Profile Form">
        
        <!-- Personal Information Section -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">Personal Information</div>
            <div class="card-body">
                <div class="row">
                    <!-- Full Name -->
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Full Name:</label>
                        <input type="text" id="name" name="name" value="{{ profile.name }}" class="form-control" placeholder="Enter full name" required>
                    </div>

                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email Address:</label>
                        <input type="email" id="email" name="email" value="{{ profile.email }}" class="form-control" placeholder="Enter email" required>
                    </div>
                </div>

                <div class="row">
                    <!-- Phone Number -->
                    <div class="col-md-6 mb-3">
                        <label for="phone_number" class="form-label">Phone Number:</label>
                        <input type="tel" id="phone_number" name="phone_number" value="{{ profile.phone_number }}" class="form-control" placeholder="Enter phone number">
                    </div>

                    <!-- Age -->
                    <div class="col-md-6 mb-3">
                        <label for="age" class="form-label">Age:</label>
                        <input type="number" id="age" name="age" value="{{ profile.age }}" class="form-control" min="0" placeholder="Enter age">
                    </div>
                </div>

                <div class="row">
                    <!-- Gender -->
                    <div class="col-md-6 mb-3">
                        <label for="gender" class="form-label">Gender:</label>
                        <select id="gender" name="gender" class="form-select">
                            <option value="Male" {% if profile.gender == 'Male' %}selected{% endif %}>Male</option>
                            <option value="Female" {% if profile.gender == 'Female' %}selected{% endif %}>Female</option>
                            <option value="Other" {% if profile.gender == 'Other' %}selected{% endif %}>Other</option>
                            <option value="Prefer not to say" {% if profile.gender == 'Prefer not to say' %}selected{% endif %}>Prefer not to say</option>
                        </select>
                    </div>

                    <!-- Location -->
                    <div class="col-md-6 mb-3">
                        <label for="location" class="form-label">Location:</label>
                        <input type="text" id="location" name="location" value="{{ profile.location }}" class="form-control" placeholder="Enter location">
                    </div>
                </div>
            </div>
        </div>

        <!-- Academic Information Section -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">Academic Information</div>
            <div class="card-body">
                <div class="row">
                    <!-- Academic Level -->
                    <div class="col-md-6 mb-3">
                        <label for="academic_level" class="form-label">Academic Level:</label>
                        <input type="text" id="academic_level" name="academic_level" value="{{ profile.academic_level }}" class="form-control" placeholder="Enter academic level">
                    </div>

                    <!-- Preferred Subject -->
                    <div class="col-md-6 mb-3">
                        <label for="preferred_subject" class="form-label">Preferred Subject:</label>
                        <input type="text" id="preferred_subject" name="preferred_subject" value="{{ profile.preferred_subject }}" class="form-control" placeholder="Enter preferred subject">
                    </div>
                </div>
            </div>
        </div>

        <!-- Learning Preferences -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">Learning Preferences</div>
            <div class="card-body">
                <div class="row">
                    <!-- Learning Style -->
                    <div class="col-md-6 mb-3">
                        <label for="learning_style" class="form-label">Learning Style:</label>
                        <input type="text" id="learning_style" name="learning_style" value="{{ profile.learning_style }}" class="form-control" placeholder="Enter learning style">
                    </div>

                    <!-- Language -->
                    <div class="col-md-6 mb-3">
                        <label for="language" class="form-label">Preferred Language:</label>
                        <input type="text" id="language" name="language" value="{{ profile.language }}" class="form-control" placeholder="Enter language">
                    </div>
                </div>
            </div>
        </div>

        <!-- User Achievements Section -->
        <div class="card mb-4">
            <div class="card-header bg-warning">User Achievements</div>
            <div class="card-body">
                <div class="row">
                    <!-- XP -->
                    <div class="col-md-6 mb-3">
                        <label for="xp" class="form-label">XP:</label>
                        <input type="number" id="xp" name="xp" value="{{ profile.xp }}" class="form-control" min="0" readonly>
                    </div>

                    <!-- Level -->
                    <div class="col-md-6 mb-3">
                        <label for="level" class="form-label">Level:</label>
                        <input type="number" id="level" name="level" value="{{ profile.level }}" class="form-control" min="1" readonly>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Checkbox -->
        <div class="form-check mb-3">
            <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin" {% if profile.is_admin %}checked{% endif %}>
            <label class="form-check-label" for="is_admin" data-bs-toggle="tooltip" title="Grant admin privileges to this user.">
                Admin Privileges
            </label>
        </div>

        <div class="text-end">
            <button type="submit" class="btn btn-success me-2" aria-label="Save Changes">Save Changes</button>
            <a href="{{ url_for('admin.view_user', username=profile.username) }}" class="btn btn-secondary" aria-label="Cancel">Cancel</a>
        </div>
    </form>
</div>

<!-- Enable Bootstrap Tooltips -->
<script>
    document.addEventListener("DOMContentLoaded", function () {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.forEach(function (tooltipTriggerEl) {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>

{% endblock %}
