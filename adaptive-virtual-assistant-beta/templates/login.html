<!-- templates/login.html -->

{% extends "base.html" %}

{% block title %}Login - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card p-4 shadow-sm">
                <h2 class="mb-4 text-center">Sign In</h2>
                
                <!-- Flash Messages for Errors -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST" action="{{ url_for('auth.login') }}" aria-label="Login Form" novalidate>
                    <div class="mb-3">
                        <label for="username" class="form-label">Username:</label>
                        <input type="text" name="username" id="username" class="form-control" 
                               placeholder="Enter your username" aria-label="Username input" 
                               required aria-required="true" autocomplete="off">
                    </div>

                    <div class="mb-4">
                        <label for="password" class="form-label">Password:</label>
                        <div class="input-group">
                            <input class="form-control" type="password" name="password" id="password" 
                                   placeholder="Enter your password" aria-label="Password input" 
                                   required aria-required="true" autocomplete="off">
                            <button id="password-view-toggle" class="btn btn-outline-secondary" type="button" 
                                    aria-label="Toggle password visibility">
                                <i class="bi bi-eye" id="password-view-icon"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary w-100" aria-label="Login">Login</button>
                </form>

                <p class="mt-3 text-center">
                    Don't have an account? 
                    <a href="{{ url_for('auth.register') }}" aria-label="Register here">Register here</a>.
                </p>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('password-view-toggle').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const passwordIcon = document.getElementById('password-view-icon');

        if (passwordField.type === "password") {
            passwordField.type = "text";
            passwordIcon.classList.remove('bi-eye');
            passwordIcon.classList.add('bi-eye-slash');
        } else {
            passwordField.type = "password";
            passwordIcon.classList.remove('bi-eye-slash');
            passwordIcon.classList.add('bi-eye');
        }
    });
</script>
{% endblock %}
