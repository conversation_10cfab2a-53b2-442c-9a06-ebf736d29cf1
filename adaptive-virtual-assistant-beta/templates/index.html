<!-- templates/index.html -->

{% extends "base.html" %}

{% block title %}Home - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1 class="mb-4 text-center">👋 Hello, I am Your Adaptive Virtual Assistant</h1>
    <p class="lead text-center">Ask me anything! Choose a predefined question or enter your own.</p>

    <form id="question-form" aria-label="Question Submission Form" class="mt-4">
        <div class="mb-3">
            <label for="predefined-question" class="form-label">Select a Predefined Question:</label>
            <select class="form-select" id="predefined-question" aria-describedby="predefinedHelp">
                <option value="" selected>📌 Choose a question...</option>
                {% for question in predefined_questions %}
                    <option value="{{ question }}">{{ question }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="mb-3">
            <label for="question" class="form-label">Or Enter Your Own Question:</label>
            <textarea class="form-control" id="question" name="question" rows="3" placeholder="💡 Type your question here..." required aria-required="true"></textarea>
        </div>

        <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary" aria-label="Submit question"><i class="bi bi-send"></i> Ask</button>
            <button type="button" id="stop-button" class="btn btn-danger" onclick="stopLoading()" style="display: none;" aria-label="Stop response">
                <i class="bi bi-x-circle"></i> Stop
            </button>
        </div>
    </form>

    <div id="answer" class="mt-4" aria-live="polite" aria-atomic="true">
        <!-- Answer will be displayed here -->
    </div>

    <!-- Knowledge check section (initially hidden) -->
    <div id="knowledge-check-container" class="mt-4 d-none">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3 class="h5 mb-0">Test Your Understanding</h3>
            </div>
            <div class="card-body">
                <p>Without looking at the answer above, write what you remember:</p>
                <textarea id="user-summary" class="form-control" rows="4" placeholder="I remember that..."></textarea>
                
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="use-ai-scoring">
                    <label class="form-check-label" for="use-ai-scoring">
                        <i class="bi bi-robot"></i> Use AI for scoring (accuracy is restricted by tokens)
                    </label>
                </div>

                <button id="check-understanding" class="btn btn-primary mt-3">Check Understanding</button>
                
                <div id="similarity-result" class="mt-3 d-none">
                    <!-- Results will appear here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let controller = null;  // Store the AbortController instance
    let isRequestActive = false;  // Track if a request is in progress
    let originalAnswer = ""; // Will store the original answer
    let answerStyle = ""; // Will store the answer style

    // Update text area when a predefined question is selected
    document.getElementById('predefined-question').addEventListener('change', function() {
        document.getElementById('question').value = this.value || '';
    });

    document.getElementById('question-form').addEventListener('submit', function(event) {
        event.preventDefault();
        
        if (isRequestActive) return;  // Prevent multiple requests

        const question = document.getElementById('question').value.trim();
        const answerDiv = document.getElementById('answer');
        const askButton = document.querySelector("#question-form button[type='submit']");
        const stopButton = document.getElementById("stop-button");

        if (!question) {
            answerDiv.innerHTML = '<div class="alert alert-warning" role="alert">⚠️ Please enter a question.</div>';
            return;
        }

        if (controller) controller.abort();  // Cancel any previous request
        controller = new AbortController();
        const signal = controller.signal;

        // Set UI to loading state
        isRequestActive = true;
        askButton.disabled = true;
        stopButton.style.display = "inline-block"; // Show Stop button
        answerDiv.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Generating your answer...</p>
            </div>
        `;

        // Set timeout to force stop if request takes too long
        const timeoutId = setTimeout(() => {
            if (isRequestActive) {
                stopLoading();
                answerDiv.innerHTML = '<div class="alert alert-warning" role="alert">⏳ The response is taking too long. Please try again.</div>';
            }
        }, 15000); // Timeout after 15 seconds

        fetch("{{ url_for('main.get_answer') }}", {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ question: question }),
            signal: signal
        })
        .then(response => response.json())
        .then(data => {
            clearTimeout(timeoutId);
            stopLoading();
            if (data.answer) {
                originalAnswer = data.answer;
                answerStyle = data.answer_style; 
                answerDiv.innerHTML = `<div class="alert alert-success" role="alert">
                    <strong>💡 Answer:</strong> ${data.answer}
                    <button id="test-understanding" class="btn btn-sm btn-info mt-2">
                        Test Your Understanding
                    </button>
                    <input type="hidden" id="answer-style" value="${answerStyle}">
                </div>`;
                
                // Add click handler for the "Test Understanding" button
                document.getElementById("test-understanding").addEventListener("click", function() {
                    document.getElementById("knowledge-check-container").classList.remove("d-none");
                });
            } else {
                answerDiv.innerHTML = `<div class="alert alert-danger" role="alert">🚨 No response received. Please try again.</div>`;
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            stopLoading();
            if (error.name === "AbortError") {
                answerDiv.innerHTML = `<div class="alert alert-warning" role="alert">⚠️ Request was stopped.</div>`;
            } else {
                answerDiv.innerHTML = `<div class="alert alert-danger" role="alert">❌ An error occurred while processing your request.</div>`;
                console.error("Error:", error);
            }
        });
    });

    // Function to stop request and reset UI
    function stopLoading() {
        if (controller) controller.abort();
        isRequestActive = false;
        document.querySelector("#question-form button[type='submit']").disabled = false;
        document.getElementById("stop-button").style.display = "none"; // Hide Stop button
        document.getElementById("answer").innerHTML = '<div class="alert alert-warning" role="alert">⚠️ Request stopped.</div>';
    }

    // Add event listener for the check understanding button
    document.addEventListener('DOMContentLoaded', function() {
        document.body.addEventListener('click', function(e) {
            if (e.target && e.target.id === 'check-understanding') {
                const userSummary = document.getElementById('user-summary').value.trim();
                if (!userSummary) {
                    alert("Please write your summary first.");
                    return;
                }
                
                checkUnderstanding(originalAnswer, userSummary);
            }
        });
    });

    // Function to check understanding
    function checkUnderstanding(originalContent, userSummary) {
        const resultDiv = document.getElementById('similarity-result');
        resultDiv.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Evaluating...</span>
                </div>
                <p class="mt-2 text-muted">Analyzing your understanding...</p>
            </div>
        `;
        resultDiv.classList.remove('d-none');
        
        // Get the toggle state
        const useAI = document.getElementById('use-ai-scoring').checked;
        
        fetch("{{ url_for('main.check_understanding') }}", {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                original_content: originalContent, 
                user_summary: userSummary,
                use_ai: useAI,
                answer_style: document.getElementById('answer-style')?.value || 'informational'
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log("Score received from backend:", data.score);
            if (data.score !== undefined) {
                const scoreClass = data.score >= 80 ? 'success' : 
                                  data.score >= 60 ? 'info' : 
                                  data.score >= 40 ? 'warning' : 'danger';
                resultDiv.innerHTML = `
                    <div class="text-center">
                        <h4>Your Understanding Score: ${data.score.toFixed(1)}/100</h4>
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar bg-${scoreClass}" role="progressbar" 
                                 style="width: ${data.score}%" aria-valuenow="${data.score}" 
                                 aria-valuemin="0" aria-valuemax="100">${data.score.toFixed(1)}%</div>
                        </div>
                        <p>${data.feedback || 'Keep learning!'}</p>
                        <small class="text-muted">Scored using ${data.source || (useAI ? 'AI' : 'local')} processing</small>
                        
                        <!-- Weight Adjustment Information -->
                        <div class="card mt-3">
                            <div class="card-header bg-light">
                                <h5 class="h6 mb-0">Learning Adaptation Metrics</h5>
                            </div>
                            <div class="card-body">
                                <p class="small mb-2">Your answer weights have been adjusted to better match your learning style.</p>
                                <div class="row">
                                    ${Object.entries(data.weights || {}).map(([type, weight]) => `
                                        <div class="col-6 mb-2">
                                            <label class="form-label small">${type.charAt(0).toUpperCase() + type.slice(1)}</label>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-info" role="progressbar" 
                                                    style="width: ${weight * 100}%;" 
                                                    aria-valuenow="${weight}" aria-valuemin="0" aria-valuemax="1">
                                                    ${weight.toFixed(2)}
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                                <small class="text-muted">
                                    <i class="bi bi-info-circle-fill me-1"></i>
                                    Weights are adjusted based on your performance. Higher scores strengthen the corresponding answer type.
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.error || 'Could not evaluate understanding'}</div>`;
            }
        })
        .catch(error => {
            resultDiv.innerHTML = `<div class="alert alert-danger">Error: Could not evaluate understanding</div>`;
            console.error("Error:", error);
        });
    }
</script>
{% endblock %}
