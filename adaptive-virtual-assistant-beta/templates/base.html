<!-- base.html - Enhanced with comments for readability and maintainability -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{% block title %}Adaptive Virtual Assistant{% endblock %}</title>
    <!-- Include viewport meta tag for responsiveness -->
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS and Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">

    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation Bar -->
<!-- Begin Header and Navigation -->
    <header role="banner">
<!-- Begin Header and Navigation -->
        <nav class="navbar navbar-expand-lg" role="navigation" aria-label="Main Navigation">
            <div class="container-fluid">
                <a class="navbar-brand" aria-label="Adaptive Virtual Assistant Home" href="{{ url_for('main.index') }}">Adaptive Virtual Assistant</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="More navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        {% if current_user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('main.history_view') }}" aria-label="Response History">Response History</a>
                            </li>
                        {% endif %}

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.leaderboard') }}" aria-label="Leaderboard">Leaderboard</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.about') }}" aria-label="About Page">About</a>
                        </li>

                        {% if current_user.is_authenticated %}
                            {% if current_user.is_admin %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('admin.admin_dashboard') }}" aria-label="Administration Dashboard">Administration Dashboard</a>
                                </li>
                            {% endif %}
                        {% endif %}
                    </ul>
                    <ul class="navbar-nav ms-auto mb-2 mb-lg-0 align-items-center">
                        <!-- Dark/Light Mode Toggle -->
                        <li class="nav-item me-3">
                            <button id="theme-toggle" class="btn btn-outline-secondary" aria-label="Toggle Dark and Light Mode">
                                <i class="bi bi-moon-fill" id="theme-icon"></i>
                            </button>
                        </li>
                        {% if current_user.is_authenticated %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ current_user.username }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="{{ url_for('main.profile_view') }}" aria-label="View profile">Profile</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}" aria-label="Logout">Logout</a></li>
                                </ul>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('auth.login') }}" aria-label="Login">Login</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('auth.register') }}" aria-label="Register">Register</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
<!-- End Header -->
    </header>


    <!-- Main Content -->
<!-- Main content begins -->
    <main class="container my-4 flex-grow-1" role="main">
        {% block content %}{% endblock %}
<!-- Main content ends -->
    </main>

    <!-- Flash Messages -->
    <div class="container mt-3" aria-live="polite" aria-atomic="true">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Footer -->
<!-- Footer starts -->
    <footer class="footer mt-auto py-3" role="contentinfo">
        <div class="container text-center">
            <span class="text-muted">&copy; 2025 Adaptive Virtual Assistant</span>
        </div>
<!-- Footer ends -->
    </footer>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" ></script>

    <!-- Dark/Light Mode Script -->
    <script>
        // Function to toggle dark mode
        function toggleDarkMode() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');
            body.classList.toggle('dark-mode');
    
            if (body.classList.contains('dark-mode')) {
                themeIcon.classList.remove('bi-sun-fill');
                themeIcon.classList.add('bi-moon-fill');
                localStorage.setItem('theme', 'dark');
            } else {
                themeIcon.classList.remove('bi-moon-fill');
                themeIcon.classList.add('bi-sun-fill');
                localStorage.setItem('theme', 'light');
            }
        }
    
        // Check if the theme toggle button exists before adding the event listener
        const themeToggleButton = document.getElementById('theme-toggle');
        if (themeToggleButton) {
            themeToggleButton.addEventListener('click', toggleDarkMode);
        }
    
        window.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme');
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');
    
            if (savedTheme === 'dark') {
                body.classList.add('dark-mode');
                themeIcon.classList.remove('bi-sun-fill');
                themeIcon.classList.add('bi-moon-fill');
            } else {
                body.classList.remove('dark-mode');
                themeIcon.classList.remove('bi-moon-fill');
                themeIcon.classList.add('bi-sun-fill');
            }
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
