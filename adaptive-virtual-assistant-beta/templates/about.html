<!-- templates/about.html -->
<!-- Adaptive Virtual Assistant v2.0 -->
<!-- This final release includes comprehensive personalization, AI integration, gamification, and admin tools. -->
<!-- NOTICE: The demo Gemini API key will be deleted after June 30, 2025. Please use your own API key configured in the .env file. -->


{% extends "base.html" %}

{% block title %}About - Adaptive Virtual Assistant{% endblock %}

{% block content %}


<div class="container mt-5">
<h1 class="mb-4 text-center">About Adaptive Virtual Assistant</h1>
<p class="lead text-center">
        Adaptive Virtual Assistant (AVA) is a smart, Python-powered learning companion. It delivers <strong>AI-driven, real-time answers</strong> that adapt to each user's <strong>goals, strengths, and learning preferences</strong> for a tailored academic experience.
    </p>
<!-- Features Section -->
<h2 class="mt-5">Key Features</h2>
<ul class="list-group mb-5 shadow-sm">
<li class="list-group-item"><strong>✅ User Authentication &amp; Authorization:</strong> Secure login and role-based access with admin controls.</li>
<li class="list-group-item"><strong>✅ Personalized AI Responses:</strong> Gemini-powered answers tailored to user preferences.</li>
<li class="list-group-item"><strong>✅ Adaptive Learning:</strong> Dynamically adjusts responses for different learning styles and academic levels.</li>
<li class="list-group-item"><strong>✅ XP &amp; Gamification:</strong> XP, streaks, levels, and badges motivate continued learning.</li>
<li class="list-group-item"><strong>✅ Leaderboard:</strong> Compare your learning progress with others in real-time.</li>
<li class="list-group-item"><strong>✅ Question History:</strong> View and search your personalized question-answer archive.</li>
<li class="list-group-item"><strong>✅ Admin Dashboard:</strong> Admins can view, edit, and manage users, goals, and metrics.</li>
<li class="list-group-item"><strong>✅ Dark Mode:</strong> Improved accessibility and aesthetics across devices.</li>
</ul>
<!-- Version History Section -->
<h2 class="mt-5">Version History</h2>
<div class="accordion accordion-flush mb-5" id="versionAccordion">
    <!-- Version 2.0 -->
<div class="accordion-item">
    <h2 class="accordion-header" id="headingV20">
      <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseV20" aria-expanded="true" aria-controls="collapseV20">
        🔺 Version 2.0 – Final Production Release (May 2025)
      </button>
    </h2>
    <div id="collapseV20" class="accordion-collapse collapse show" aria-labelledby="headingV20" data-bs-parent="#versionAccordion">
      <div class="accordion-body">
        <ul>
          <li>🎓 Adaptive answering based on profile and answer-type weights</li>
          <li>🔐 Role-based login with user/admin separation</li>
          <li>🏆 Gamification: XP, levels, badges, and streaks</li>
          <li>📚 Searchable Q&A history with pagination</li>
          <li>🛠 Admin dashboard and user management tools</li>
          <li>🧠 Personalized responses powered by Google Gemini AI</li>
          <li>⚠️ <strong>Notice:</strong> Demo Gemini API key will be <u>removed after June 30, 2025</u>; please update your <code>.env</code> file with your own key</li>
        </ul>
      </div>
    </div>
  </div>
  
<!-- Version 1.5 -->
<div class="accordion-item">
<h2 class="accordion-header" id="headingV15">
<button aria-controls="collapseV15" aria-expanded="true" class="accordion-button" data-bs-target="#collapseV15" data-bs-toggle="collapse" type="button">
                    🔸 Version 1.5 – UI + Functional Enhancements (April 2025)
                </button>
</h2>
<div aria-labelledby="headingV15" class="accordion-collapse collapse show" data-bs-parent="#versionAccordion" id="collapseV15">
<div class="accordion-body">
<ul>
<li>🧠 Smart Profile Weights: Users now assign weight values to answer types (e.g. real-world vs. informational).</li>
<li>🔍 Improved History Search: Live, keyword-filtered, zero-padding history layout with pagination.</li>
<li>📊 Enhanced Leaderboard: Streaks, XP, levels, and badge displays now responsive and polished.</li>
<li>📋 Redesigned Admin Dashboard: Clean full-page table, color-coded status, and easier UX.</li>
<li>🎨 About Page Update: New structure, team section styling, and contribution instructions.</li>
</ul>
</div>
</div>
</div>
<!-- Version 1.4 -->
<div class="accordion-item">
<h2 class="accordion-header" id="headingV14">
<button aria-controls="collapseV14" aria-expanded="false" class="accordion-button collapsed" data-bs-target="#collapseV14" data-bs-toggle="collapse" type="button">
                    🔹 Version 1.4 – Gamification &amp; UX Overhaul
                </button>
</h2>
<div aria-labelledby="headingV14" class="accordion-collapse collapse" id="collapseV14">
<div class="accordion-body">
<ul>
<li>🏆 Added XP system, level progression, and badge rewards.</li>
<li>🌙 Dark mode styling improvements.</li>
<li>🔍 History page now includes search and improved pagination.</li>
</ul>
</div>
</div>
</div>
<!-- Version 1.3 -->
<div class="accordion-item">
<h2 class="accordion-header" id="headingV13">
<button aria-controls="collapseV13" aria-expanded="false" class="accordion-button collapsed" data-bs-target="#collapseV13" data-bs-toggle="collapse" type="button">
                    🔹 Version 1.3 – AI-Powered Smart Responses
                </button>
</h2>
<div aria-labelledby="headingV13" class="accordion-collapse collapse" id="collapseV13">
<div class="accordion-body">
<ul>
<li>🤖 Google Gemini API integration for context-aware answers.</li>
<li>📖 Markdown support added for styled, readable responses.</li>
</ul>
</div>
</div>
</div>
</div>
<!-- Setup Instructions -->
<h2 class="mt-5">Quick Start Setup</h2>
<pre><code># Clone the Repository
git clone https://github.com/your-username/adaptive-virtual-assistant-beta.git

# Open the Directory
cd adaptive-virtual-assistant-beta

# Create and Activate Virtual Environment
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows

# Install Dependencies
pip install -r requirements.txt

# Run the Application
python app.py
    </code></pre>
<!-- Contribution -->
<h2 class="mt-5">Contribute to AVA</h2>
<p>We welcome collaboration! Here's how to get started:</p>
<ol>
<li>Fork this repository on GitHub.</li>
<li>Create a new branch with your changes.</li>
<li>Commit and push to your branch.</li>
<li>Submit a pull request and tag an issue if applicable.</li>
</ol>
<!-- Team Members -->
<h2 class="mt-5">Meet Our Team</h2>
<div class="row text-center">
        {% for member in [
            {"name": "Vivien Stahl", "role": "Developer", "img": "team_member_1.jpg"},
            {"name": "Preet Patel", "role": "Developer", "img": "team_member_2.jpg"},
            {"name": "Maxwell Hicks", "role": "UX Designer", "img": "team_member_3.jpg"},
            {"name": "Sumair Mawji", "role": "Developer", "img": "team_member_4.jpg"},
            {"name": "David Berry", "role": "Project Liaison", "img": "team_member_5.jpg"}
        ] %}
        <div class="col-md-4 col-sm-6 mb-4">
<div class="card h-100 shadow-sm team-card">
<div class="card-body d-flex flex-column align-items-center">
<div class="team-image mb-3" style="background-image: url('{{ url_for('static', filename='images/' ~ member.img) }}');"></div>
<h5 class="card-title">{{ member.name }}</h5>
<p class="card-text">{{ member.role }}</p>
</div>
</div>
</div>
        {% endfor %}
    </div>
<!-- Contact -->
<h2 class="mt-5">Contact Us</h2>
<p>If you have questions or want to reach out, email us at: <a href="mailto:<EMAIL>"><EMAIL></a></p>
</div>
<!-- Custom Styles -->
<style>
    .team-card:hover {
        transform: scale(1.05);
        transition: transform 0.3s ease;
    }

    .team-image {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-size: cover;
        background-position: center;
    }

    pre {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        overflow-x: auto;
    }

    .accordion-button {
        font-weight: 500;
    }

    .accordion-item ul {
        padding-left: 1.2rem;
        list-style-type: square;
    }
</style>
{% endblock %}
