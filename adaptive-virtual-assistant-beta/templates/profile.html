<!-- templates/profile.html -->

{% extends "base.html" %}

{% block title %}Profile - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="card shadow-sm">
        <div class="card-body">
            <h4>User Information</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Username:</dt>
                <dd class="col-sm-8">{{ profile.username }}</dd>

                <dt class="col-sm-4 fw-bold">Email:</dt>
                <dd class="col-sm-8"><a href="mailto:{{ profile.email }}">{{ profile.email }}</a></dd>

                <dt class="col-sm-4 fw-bold">Phone Number:</dt>
                <dd class="col-sm-8"><a href="tel:{{ profile.phone_number }}">{{ profile.phone_number }}</a></dd>

                <dt class="col-sm-4 fw-bold">Age:</dt>
                <dd class="col-sm-8">{{ profile.age }}</dd>

                <dt class="col-sm-4 fw-bold">Gender:</dt>
                <dd class="col-sm-8">{{ profile.gender[0:1].upper() }}{{ profile.gender[1:] }}</dd>
            </dl>

            <h4 class="mt-4">Academic Information</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Academic Level:</dt>
                <dd class="col-sm-8">{{ profile.academic_level }}</dd>

                <dt class="col-sm-4 fw-bold">Preferred Subject:</dt>
                <dd class="col-sm-8">{{ profile.preferred_subject[0:1].upper() }}{{ profile.preferred_subject[1:] }}</dd>

                <dt class="col-sm-4 fw-bold">Learning Style:</dt>
                <dd class="col-sm-8">{{ profile.learning_style[0:1].upper() }}{{ profile.learning_style[1:] }}</dd>
            </dl>

            <h4 class="mt-4">Gamification & Progress</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">XP (Experience Points):</dt>
                <dd class="col-sm-8">
                    <strong>{{ profile.xp }} XP</strong>
                </dd>

                <dt class="col-sm-4 fw-bold">Level:</dt>
                <dd class="col-sm-8">
                    Level {{ profile.level }}
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ (profile.xp / (profile.level * 100)) * 100 }}%" 
                             aria-valuenow="{{ profile.xp }}" 
                             aria-valuemin="0" 
                             aria-valuemax="{{ profile.level * 100 }}">
                        </div>
                    </div>
                </dd>

                <dt class="col-sm-4 fw-bold">Streak:</dt>
                <dd class="col-sm-8">
                    🔥 {{ profile.streak }}-day streak
                </dd>

                <dt class="col-sm-4 fw-bold">Achievements:</dt>
                <dd class="col-sm-8">
                    {% if profile.badges %}
                        {% for badge in profile.badges %}
                            <span class="badge bg-primary p-2 me-1">🏆 {{ badge }}</span>
                        {% endfor %}
                    {% else %}
                        <span class="text-muted">No achievements yet. Keep learning!</span>
                    {% endif %}
                </dd>
            </dl>

            <h4 class="mt-4">Location Information</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Location:</dt>
                <dd class="col-sm-8">{{ profile.location }}</dd>

                <dt class="col-sm-4 fw-bold">Timezone:</dt>
                <dd class="col-sm-8">{{ profile.timezone }}</dd>

                <dt class="col-sm-4 fw-bold">Language:</dt>
                <dd class="col-sm-8">{{ profile.language[0:1].upper() }}{{ profile.language[1:] }}</dd>

                <dt class="col-sm-4 fw-bold">Cultural Background:</dt>
                <dd class="col-sm-8">{{ profile.cultural_background[0:1].upper() }}{{ profile.cultural_background[1:] }}</dd>
            </dl>

            <h4 class="mt-4">User Preferences & Interests</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Hobbies:</dt>
                <dd class="col-sm-8">{{ profile.hobbies | join(", ") }}</dd>

                <dt class="col-sm-4 fw-bold">Goals:</dt>
                <dd class="col-sm-8">{{ profile.goals | join(", ") }}</dd>

                <dt class="col-sm-4 fw-bold">Strengths:</dt>
                <dd class="col-sm-8">{{ profile.strengths | join(", ") }}</dd>

                <dt class="col-sm-4 fw-bold">Weaknesses:</dt>
                <dd class="col-sm-8">{{ profile.weaknesses | join(", ") }}</dd>
            </dl>
        </div>
        <div class="card-footer text-end">
            <a href="{{ url_for('main.update_preferences') }}" class="btn btn-primary me-2 text-decoration-none">Update Preferences</a>
            <a href="{{ url_for('main.leaderboard') }}" class="btn btn-info text-decoration-none">View Leaderboard</a>
            <a href="{{ url_for('main.index') }}" class="btn btn-secondary text-decoration-none">Back to Home</a>
        </div>
    </div>
</div>
{% endblock %}
