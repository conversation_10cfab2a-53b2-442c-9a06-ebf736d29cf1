<!-- templates/view_user.html -->

{% extends "base.html" %}

{% block title %}View User - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="card shadow-lg">
        <div class="card-header bg-gradient-primary text-center">
            <h3 class="mb-0"><i class="bi bi-person-circle me-2"></i> User Profile</h3>
        </div>
        <div class="card-body">
            <h4 class="mb-3 text-primary"><i class="bi bi-info-circle me-2"></i> Personal Information</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Username:</dt>
                <dd class="col-sm-8">{{ profile.username }}</dd>

                <dt class="col-sm-4 fw-bold">Email:</dt>
                <dd class="col-sm-8"><a href="mailto:{{ profile.email }}" class="text-decoration-none text-primary">{{ profile.email }}</a></dd>

                <dt class="col-sm-4 fw-bold">Phone Number:</dt>
                <dd class="col-sm-8"><a href="tel:{{ profile.phone_number }}" class="text-decoration-none text-primary">{{ profile.phone_number }}</a></dd>

                <dt class="col-sm-4 fw-bold">Age:</dt>
                <dd class="col-sm-8">{{ profile.age }}</dd>

                <dt class="col-sm-4 fw-bold">Gender:</dt>
                <dd class="col-sm-8">{{ profile.gender.capitalize() }}</dd>
            </dl>

            <h4 class="mt-4 text-primary"><i class="bi bi-book-half me-2"></i> Academic Information</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Academic Level:</dt>
                <dd class="col-sm-8">{{ profile.academic_level }}</dd>

                <dt class="col-sm-4 fw-bold">Preferred Subject:</dt>
                <dd class="col-sm-8">{{ profile.preferred_subject.capitalize() }}</dd>

                <dt class="col-sm-4 fw-bold">Learning Style:</dt>
                <dd class="col-sm-8">{{ profile.learning_style.capitalize() }}</dd>
            </dl>

            <h4 class="mt-4 text-primary"><i class="bi bi-geo-alt-fill me-2"></i> Location & Language</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Location:</dt>
                <dd class="col-sm-8">{{ profile.location }}</dd>

                <dt class="col-sm-4 fw-bold">Timezone:</dt>
                <dd class="col-sm-8">{{ profile.timezone }}</dd>

                <dt class="col-sm-4 fw-bold">Language:</dt>
                <dd class="col-sm-8">{{ profile.language.capitalize() }}</dd>

                <dt class="col-sm-4 fw-bold">Cultural Background:</dt>
                <dd class="col-sm-8">{{ profile.cultural_background.capitalize() }}</dd>
            </dl>

            <h4 class="mt-4 text-primary"><i class="bi bi-heart-fill me-2"></i> Preferences & Interests</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Hobbies:</dt>
                <dd class="col-sm-8">{{ profile.hobbies | join(", ") }}</dd>

                <dt class="col-sm-4 fw-bold">Goals:</dt>
                <dd class="col-sm-8">{{ profile.goals | join(", ") }}</dd>

                <dt class="col-sm-4 fw-bold">Strengths:</dt>
                <dd class="col-sm-8">{{ profile.strengths | join(", ") }}</dd>

                <dt class="col-sm-4 fw-bold">Weaknesses:</dt>
                <dd class="col-sm-8">{{ profile.weaknesses | join(", ") }}</dd>
            </dl>

            <h4 class="mt-4 text-primary"><i class="bi bi-award-fill me-2"></i> Achievements</h4>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Experience Points (XP):</dt>
                <dd class="col-sm-8"><span class="badge bg-success">{{ profile.xp }}</span></dd>

                <dt class="col-sm-4 fw-bold">Level:</dt>
                <dd class="col-sm-8"><span class="badge bg-info">Level {{ profile.level }}</span></dd>

                <dt class="col-sm-4 fw-bold">Streak:</dt>
                <dd class="col-sm-8"><span class="badge bg-warning">{{ profile.streak }} Days</span></dd>

                <dt class="col-sm-4 fw-bold">Badges:</dt>
                <dd class="col-sm-8">
                    {% for badge in profile.badges %}
                        <span class="badge bg-secondary">{{ badge }}</span>
                    {% endfor %}
                </dd>
            </dl>
        </div>
        {% if current_user.is_admin %}
            <div class="card mt-4">
                <div class="card-header bg-dark text-white">
                    <h4 class="h5 mb-0"><i class="bi bi-sliders me-2"></i> Learning Adaptation Metrics (Admin Only)</h4>
                </div>
                <div class="card-body">
                    <p class="mb-3">This user's answer type weights have been adjusted <strong>{{ profile.weight_adjustment_count }}</strong> times.</p>
                    
                    <h5>Current Weight Distribution:</h5>
                    <div class="row">
                        {% for type, weight in profile.answer_type_weights.items() %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label">{{ type|capitalize }}</label>
                            <div class="mb-1">{{ weight }}</div>
                            <progress 
                                class="w-100" 
                                value="{{ weight }}" 
                                max="1" 
                                style="height: 25px;">
                                {{ weight * 100 }}%
                            </progress>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <small>
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <strong>How adaptation works:</strong> Weights adjust based on user's demonstrated comprehension of different answer styles.
                            Higher similarity scores increase the corresponding answer type weight.
                            Adjustment rate decreases over time as the system learns user preferences (current adjustment strength: {{ (0.1 / (1 + profile.weight_adjustment_count / 5))|round(3) }}).
                        </small>
                    </div>
                </div>
            </div>
        {% endif %}
        <div class="card-footer text-end">
            <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-secondary me-2"><i class="bi bi-arrow-left me-1"></i>Back to Dashboard</a>
            {% if current_user.is_admin %}
                <a href="{{ url_for('admin.edit_user', username=profile.username) }}" class="btn btn-primary"><i class="bi bi-pencil-fill me-1"></i>Edit Profile</a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
