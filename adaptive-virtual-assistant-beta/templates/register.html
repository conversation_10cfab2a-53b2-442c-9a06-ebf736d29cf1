<!-- templates/register.html -->

{% extends "base.html" %}

{% block title %}Register - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card p-4 shadow-lg">
                <div class="card-header bg-gradient-primary text-black text-center">
                    <h2 class="mb-0">Create an Account</h2>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('auth.register') }}" aria-label="Registration Form">

                        <!-- Required Fields -->
                        <div class="mb-3">
                            <label for="name" class="form-label"><i class="bi bi-person-circle me-2"></i>Full Name:</label>
                            <input type="text" id="name" name="name" class="form-control" required pattern="^[A-Za-z\s]{2,50}$"
                                   title="Only letters and spaces allowed. 2–50 characters." placeholder="Enter your full name">
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label"><i class="bi bi-person-fill me-2"></i>Username:</label>
                            <input type="text" id="username" name="username" class="form-control" required pattern="^[a-zA-Z0-9_]{4,25}$"
                                   title="Use 4–25 characters: letters, numbers, or underscores." placeholder="Choose a username">
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label"><i class="bi bi-envelope-fill me-2"></i>Email:</label>
                            <input type="email" id="email" name="email" class="form-control" required placeholder="Enter your email">
                        </div>

                        <div class="mb-3">
                            <label for="age" class="form-label"><i class="bi bi-calendar-check-fill me-2"></i>Age:</label>
                            <input type="number" id="age" name="age" class="form-control" required min="3" max="120"
                                   placeholder="Enter your age" title="Must be between 3 and 120.">
                        </div>

                        <div class="mb-3">
                            <label for="academic_level" class="form-label"><i class="bi bi-book-fill me-2"></i>Academic Level:</label>
                            <input type="text" id="academic_level" name="academic_level" class="form-control" required maxlength="50"
                                   placeholder="e.g. Kindergarten, Elementary">
                        </div>

                        <div class="mb-3">
                            <label for="preferred_subject" class="form-label"><i class="bi bi-journal-text me-2"></i>Preferred Subject:</label>
                            <input type="text" id="preferred_subject" name="preferred_subject" class="form-control" required maxlength="50"
                                   placeholder="Enter your favorite subject">
                        </div>

                        <div class="mb-3">
                            <label for="learning_style" class="form-label"><i class="bi bi-lightbulb-fill me-2"></i>Learning Style:</label>
                            <select id="learning_style" name="learning_style" class="form-select" required>
                                <option value="">-- Select learning style --</option>
                                <option value="Visual">Visual</option>
                                <option value="Auditory">Auditory</option>
                                <option value="Kinesthetic">Kinesthetic</option>
                            </select>
                        </div>

                        <!-- Optional Fields -->
                        <div class="mb-3">
                            <label for="phone_number" class="form-label"><i class="bi bi-phone-fill me-2"></i>Phone Number:</label>
                            <input type="tel" id="phone_number" name="phone_number" class="form-control"
                                   pattern="^\d{3}-\d{3}-\d{4}$" placeholder="************"
                                   title="Use format: ************">
                        </div>

                        <div class="mb-3">
                            <label for="location" class="form-label"><i class="bi bi-geo-alt-fill me-2"></i>Location:</label>
                            <input type="text" id="location" name="location" class="form-control" maxlength="50" placeholder="Enter your country">
                        </div>

                        <div class="mb-3">
                            <label for="language" class="form-label"><i class="bi bi-translate me-2"></i>Language:</label>
                            <input type="text" id="language" name="language" class="form-control" maxlength="30" placeholder="Preferred language">
                        </div>

                        <div class="mb-3">
                            <label for="timezone" class="form-label"><i class="bi bi-clock-fill me-2"></i>Timezone:</label>
                            <input type="text" id="timezone" name="timezone" class="form-control" maxlength="50" placeholder="e.g. America/New_York">
                        </div>

                        <div class="mb-3">
                            <label for="hobbies" class="form-label"><i class="bi bi-palette-fill me-2"></i>Hobbies:</label>
                            <input type="text" id="hobbies" name="hobbies" class="form-control" maxlength="100" placeholder="e.g. Drawing, Playing with blocks">
                        </div>

                        <div class="mb-3">
                            <label for="goals" class="form-label"><i class="bi bi-bullseye me-2"></i>Goals:</label>
                            <input type="text" id="goals" name="goals" class="form-control" maxlength="100" placeholder="e.g. Learn to read, Make friends">
                        </div>

                        <div class="mb-3">
                            <label for="strengths" class="form-label"><i class="bi bi-star-fill me-2"></i>Strengths:</label>
                            <input type="text" id="strengths" name="strengths" class="form-control" maxlength="100" placeholder="e.g. Creativity, Imagination">
                        </div>

                        <div class="mb-3">
                            <label for="weaknesses" class="form-label"><i class="bi bi-exclamation-circle-fill me-2"></i>Weaknesses:</label>
                            <input type="text" id="weaknesses" name="weaknesses" class="form-control" maxlength="100" placeholder="e.g. Short attention span">
                        </div>

                        <!-- Password Fields -->
                        <div class="mb-3">
                            <label for="password" class="form-label"><i class="bi bi-lock-fill me-2"></i>Password:</label>
                            <input type="password" id="password" name="password" class="form-control" required minlength="6" maxlength="32"
                                   placeholder="Enter your password">
                        </div>

                        <div class="mb-4">
                            <label for="confirm_password" class="form-label"><i class="bi bi-lock-fill me-2"></i>Confirm Password:</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control" required minlength="6" maxlength="32"
                                   placeholder="Re-enter your password">
                        </div>

                        <button type="submit" class="btn btn-primary w-100">Register</button>
                    </form>

                    <p class="mt-3 text-center">
                        Already have an account? <a href="{{ url_for('auth.login') }}" class="text-primary">Login here</a>.
                    </p>

                    <script>
                        document.addEventListener('DOMContentLoaded', () => {
                            const form = document.querySelector('form');
                            const password = document.getElementById('password');
                            const confirmPassword = document.getElementById('confirm_password');
                    
                            form.addEventListener('submit', (e) => {
                                // Password match check
                                if (password.value !== confirmPassword.value) {
                                    e.preventDefault();
                                    confirmPassword.setCustomValidity("Passwords do not match.");
                                    confirmPassword.reportValidity();
                                } else {
                                    confirmPassword.setCustomValidity("");
                                }
                    
                    
                            });
                        });
                    </script>
                    
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
