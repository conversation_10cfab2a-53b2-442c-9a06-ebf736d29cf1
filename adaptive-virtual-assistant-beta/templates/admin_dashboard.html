{% extends "base.html" %}
{% block title %}Admin Dashboard - Adaptive Virtual Assistant{% endblock %}
{% block content %}

<!-- DataTables CSS & JS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.5/css/dataTables.bootstrap5.min.css">
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
<script src="https://cdn.datatables.net/1.13.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.5/js/dataTables.bootstrap5.min.js"></script>

<div class="container-fluid mt-4">
    <div class="card shadow-sm border-0 p-4 bg-white">
        <div class="card-header text-center fs-4 fw-semibold">
            👩‍💻 Admin Dashboard
        </div>
        <div class="card-body">
            <div class="alert alert-warning text-center" role="alert">
                ⚠️ This page only supports <strong>light mode</strong>. Dark mode is disabled here for best readability.
            </div>

            <div class="text-end mb-3">
                <label for="columnFilter" class="form-label fw-semibold me-2">Filter by Admin:</label>
                <select id="columnFilter" class="form-select d-inline-block w-auto" aria-label="Filter by Admin">
                    <option value="">All</option>
                    <option value="Yes">Yes</option>
                    <option value="No">No</option>
                </select>
            </div>

            <div class="table-responsive">
                <table id="adminTable" class="table table-hover table-bordered table-striped align-middle text-nowrap">
                    <thead class="table-dark text-center">
                        <tr>
                            <th>User ID</th>
                            <th>Username</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>XP</th>
                            <th>Level</th>
                            <th>Streak</th>
                            <th>Badges</th>
                            <th>Admin</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for username, profile in users.items() %}
                        <tr>
                            <td>{{ profile.user_id }}</td>
                            <td>{{ profile.username }}</td>
                            <td>{{ profile.name or "N/A" }}</td>
                            <td><a href="mailto:{{ profile.email }}">{{ profile.email }}</a></td>
                            <td><span class="badge bg-primary">{{ profile.xp }}</span></td>
                            <td><span class="badge bg-info text-dark">{{ profile.level }}</span></td>
                            <td><span class="badge bg-warning text-dark">{{ profile.streak }}</span></td>
                            <td>
                                {% if profile.badges %}
                                    {% for badge in profile.badges %}
                                        <span class="badge bg-success me-1">{{ badge }}</span>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-muted">None</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if profile.is_admin %}
                                    <span class="badge bg-success">Yes</span>
                                {% else %}
                                    <span class="badge bg-secondary">No</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{{ url_for('admin.view_user', username=profile.username) }}" class="btn btn-sm btn-outline-primary" aria-label="View user {{ profile.username }}">View</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        const table = $('#adminTable').DataTable({
            paging: true,
            pageLength: 10,
            ordering: true,
            order: [[4, 'desc']], // Sort by XP
            scrollX: true,
            language: {
                searchPlaceholder: "Search users...",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ users"
            }
        });

        $('#columnFilter').on('change', function () {
            table.column(8).search(this.value).draw();
        });

        // Force light mode regardless of user settings
        document.body.classList.remove('dark-mode');
    });
</script>

<style>
    body.dark-mode, .dark-mode * {
        background-color: unset !important;
        color: unset !important;
    }

    .badge {
        font-size: 0.8rem;
        padding: 0.5em 0.6em;
    }

    .form-select {
        max-width: 160px;
    }

    .table th, .table td {
        vertical-align: middle;
        text-align: center;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.3rem 0.8rem;
        margin: 0 0.1rem;
    }

    .dataTables_wrapper .dataTables_filter input {
        margin-left: 0.5em;
        padding: 0.4em;
        width: 200px;
    }
</style>

{% endblock %}
