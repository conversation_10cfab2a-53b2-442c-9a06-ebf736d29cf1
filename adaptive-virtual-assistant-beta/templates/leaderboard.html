{% extends "base.html" %}
{% block title %}Leaderboard - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container py-5">
    <h2 class="text-center mb-2">🏆 Leaderboard</h2>
    <p class="text-center text-muted">Celebrate top learners and keep the streak going!</p>

    {% if users %}
    <div class="table-responsive mt-4">
        <table class="table table-hover align-middle">
            <thead class="table-dark">
                <tr class="text-center">
                    <th scope="col">Rank</th>
                    <th scope="col">Username</th>
                    <th scope="col">Name</th>
                    <th scope="col">XP</th>
                    <th scope="col">Progress</th>
                    <th scope="col">Level</th>
                    <th scope="col">Streak</th>
                    <th scope="col">Badges</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr class="text-center">
                    <td>
                        {% if loop.index == 1 %}
                            🥇
                        {% elif loop.index == 2 %}
                            🥈
                        {% elif loop.index == 3 %}
                            🥉
                        {% else %}
                            {{ loop.index }}
                        {% endif %}
                    </td>
                    <td><strong>{{ user.username }}</strong></td>
                    <td>{{ user.name or "N/A" }}</td>
                    <td>{{ user.xp }}</td>
                    <td>
                        <div class="progress" role="progressbar" aria-label="XP progress" aria-valuenow="{{ user.xp % 100 }}" aria-valuemin="0" aria-valuemax="100">
                            <div class="progress-bar bg-info" style="width: {{ user.xp % 100 }}%;">
                                {{ user.xp % 100 }}%
                            </div>
                        </div>
                    </td>
                    <td><span class="badge bg-primary">Lv {{ user.level }}</span></td>
                    <td><span class="badge bg-warning text-dark">{{ user.streak }} 🔥</span></td>
                    <td>
                        {% if user.badges %}
                            {% for badge in user.badges %}
                                <span class="badge bg-success">{{ badge }}</span>
                            {% endfor %}
                        {% else %}
                            <span class="text-muted">None</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
        <div class="alert alert-info text-center mt-4">No leaderboard data available yet.</div>
    {% endif %}

    <div class="text-center mt-4">
        <a href="{{ url_for('main.index') }}" class="btn btn-outline-primary">← Back to Home</a>
    </div>
</div>
{% endblock %}
