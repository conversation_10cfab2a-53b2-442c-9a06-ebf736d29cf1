{% extends "base.html" %}

{% block title %}Question History - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container py-5">
    <h2 class="mb-4 text-center">🕘 Question History</h2>

    <!-- Search Bar -->
    <form method="GET" action="{{ url_for('main.history_view') }}"
          class="sticky-top z-1 shadow-sm rounded" style="top: 75px;"
          aria-label="Search History">
        <div class="input-group mb-3">
            <input type="text" class="form-control" name="q" placeholder="🔍 Search your history..." value="{{ q }}">
            <button class="btn btn-primary" type="submit">Search</button>
        </div>
    </form>

    <!-- Export Buttons -->
    <div class="d-flex justify-content-end gap-2 mb-3">
        <button class="btn btn-outline-secondary btn-sm" disabled>Export as CSV</button>
        <button class="btn btn-outline-secondary btn-sm" disabled>Export as PDF</button>
    </div>

    {% if history %}
    <!-- History Grid -->
    <div class="row row-cols-1 row-cols-md-2 g-4">
        {% for entry in history %}
        <div class="col">
            <div class="card h-100 shadow-sm border-0 bg-light dark-mode-bg">
                <div class="card-header d-flex justify-content-between align-items-center bg-white dark-mode-bg border-bottom">
                    <div>
                        <small class="text-muted">{{ entry.timestamp }}</small>
                        {% if entry.tags %}
                            <div class="mt-1">
                                {% for tag in entry.tags %}
                                    <span class="badge bg-info text-dark me-1">{{ tag }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleAnswer(this)">
                        <i class="bi bi-eye"></i> Toggle Answer
                    </button>
                </div>
                
                <div class="card-body d-flex flex-column">
                    <p><strong>❓ Question:</strong> {{ entry.question }}</p>
                    <div class="answer-body collapse show">
                        <p><strong>💡 Answer:</strong></p>
                        <div class="bg-light dark-mode-box border rounded p-2 answer-content">{{ entry.answer | safe }}</div>
                        <button class="btn btn-sm btn-outline-dark mt-2 copy-btn" onclick="copyAnswer(this)">
                            <i class="bi bi-clipboard"></i> Copy Answer
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
<!-- Begin Header and Navigation -->
    <nav aria-label="Page navigation" class="mt-5">
        <ul class="pagination justify-content-center">
            {% if page > 1 %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.history_view', page=page-1, q=q) }}">&laquo; Prev</a>
            </li>
            {% else %}
            <li class="page-item disabled"><span class="page-link">&laquo; Prev</span></li>
            {% endif %}

            {% for p in range(1, pages + 1) %}
                {% if p == page %}
                <li class="page-item active"><span class="page-link">{{ p }}</span></li>
                {% else %}
                <li class="page-item"><a class="page-link" href="{{ url_for('main.history_view', page=p, q=q) }}">{{ p }}</a></li>
                {% endif %}
            {% endfor %}

            {% if page < pages %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.history_view', page=page+1, q=q) }}">Next &raquo;</a>
            </li>
            {% else %}
            <li class="page-item disabled"><span class="page-link">Next &raquo;</span></li>
            {% endif %}
        </ul>
    </nav>

    <!-- Clear History -->
    <form method="POST" action="{{ url_for('main.clear_history') }}" class="mt-4 text-center">
        <button type="submit" class="btn btn-danger px-4"
                onclick="return confirm('Are you sure you want to clear your history?');">
            🧹 Clear History
        </button>
    </form>

    {% else %}
    <div class="alert alert-info text-center mt-4" role="alert">
        No question history found. Ask something!
    </div>
    {% endif %}
</div>

<!-- JavaScript Enhancements -->
<script>
function toggleAnswer(button) {
    const cardBody = button.closest('.card').querySelector('.answer-body');
    cardBody.classList.toggle('show');
    cardBody.classList.toggle('collapse');
}

function copyAnswer(button) {
    const text = button.closest('.card-body').querySelector('.answer-content').innerText;
    navigator.clipboard.writeText(text).then(() => {
        button.innerHTML = '<i class="bi bi-check-circle-fill"></i> Copied!';
        setTimeout(() => button.innerHTML = '<i class="bi bi-clipboard"></i> Copy Answer', 1500);
    });
}
</script>

<!-- Dark Mode CSS -->
<style>
    .dark-mode .card,
    .dark-mode .bg-light {
        background-color: #1e1e1e !important;
        color: #e0e0e0;
    }

    .dark-mode .card-header {
        background-color: #2c2c2c !important;
        border-bottom: 1px solid #444;
    }

    .dark-mode .answer-content {
        background-color: #2b2b2b !important;
        border: 1px solid #444;
    }

    .dark-mode .btn-outline-dark {
        border-color: #888;
        color: #ddd;
    }

    .dark-mode .btn-outline-dark:hover {
        background-color: #444;
    }

    .dark-mode .pagination .page-link {
        background-color: #2c2c2c;
        border-color: #444;
        color: #ccc;
    }

    .dark-mode .pagination .active > .page-link {
        background-color: #555;
        border-color: #777;
    }

    .dark-mode .bg-body {
        background-color: #121212 !important;
    }
</style>
{% endblock %}
