{% extends "base.html" %}

{% block title %}Update Preferences - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="card p-4 shadow-sm">
        <h2 class="mb-4 text-center">Update Your Preferences</h2>
        <form method="POST" action="{{ url_for('main.update_preferences') }}" aria-label="Update Preferences Form">
            <div class="mb-4">
                <label for="email" class="form-label">Email:</label>
                <input type="text" id="email" name="email" value="{{ profile.email }}" class="form-control" required aria-required="true">
            </div>

            <div class="mb-4">
                <label for="phone_number" class="form-label">Phone Number:</label>
                <input type="text" id="phone_number" name="phone_number" value="{{ profile.phone_number }}" class="form-control" required aria-required="true">
            </div>

            <div class="mb-4">
                <label for="age" class="form-label">Age:</label>
                <input type="text" id="age" name="age" value="{{ profile.age }}" class="form-control" required aria-required="true">
            </div>

            <div class="mb-4">
                <label for="academic_level" class="form-label">Academic Level:</label>
                <select id="academic_level" name="academic_level" class="form-select" required>
                    <option value="Kindergarten" {% if profile.academic_level == 'Kindergarten' %}selected{% endif %}>Kindergarten</option>
                    <option value="Elementary School" {% if profile.academic_level == 'Elementary School' %}selected{% endif %}>Elementary School</option>
                    <option value="Middle School" {% if profile.academic_level == 'Middle School' %}selected{% endif %}>Middle School</option>
                    <option value="High School" {% if profile.academic_level == 'High School' %}selected{% endif %}>High School</option>
                    <option value="College" {% if profile.academic_level == 'College' %}selected{% endif %}>College</option>
                    <option value="Graduate" {% if profile.academic_level == 'Graduate' %}selected{% endif %}>Graduate</option>
                    <option value="Bachelor's Degree" {% if profile.academic_level == "Bachelor's Degree" %}selected{% endif %}>Bachelor's Degree</option>
                    <option value="Master's Degree" {% if profile.academic_level == "Master's Degree" %}selected{% endif %}>Master's Degree</option>
                </select>
            </div>

            <div class="mb-4">
                <label for="learning_style" class="form-label">Learning Style:</label>
                <select id="learning_style" name="learning_style" class="form-select" required>
                    <option value="visual" {% if profile.learning_style == 'visual' %}selected{% endif %}>Visual</option>
                    <option value="auditory" {% if profile.learning_style == 'auditory' %}selected{% endif %}>Auditory</option>
                    <option value="kinesthetic" {% if profile.learning_style == 'kinesthetic' %}selected{% endif %}>Kinesthetic</option>
                    <option value="logical" {% if profile.learning_style == 'logical' %}selected{% endif %}>Logical</option>
                    <option value="social" {% if profile.learning_style == 'social' %}selected{% endif %}>Social</option>
                </select>
            </div>

            <div class="mb-4">
                <label for="language" class="form-label">Primary Language:</label>
                <select id="language" name="language" class="form-select" required>
                    <option value="English" {% if profile.language == 'English' %}selected{% endif %}>English</option>
                    <option value="Spanish" {% if profile.language == 'Spanish' %}selected{% endif %}>Spanish</option>
                    <option value="Mandarin" {% if profile.language == 'Mandarin' %}selected{% endif %}>Mandarin</option>
                    <option value="Hindi" {% if profile.language == 'Hindi' %}selected{% endif %}>Hindi</option>
                    <option value="Russian" {% if profile.language == 'Russian' %}selected{% endif %}>Russian</option>
                    <option value="Japanese" {% if profile.language == 'Japanese' %}selected{% endif %}>Japanese</option>
                    <option value="Arabic" {% if profile.language == 'Arabic' %}selected{% endif %}>Arabic</option>
                    <option value="French" {% if profile.language == 'French' %}selected{% endif %}>French</option>
                    <option value="German" {% if profile.language == 'German' %}selected{% endif %}>German</option>
                </select>
            </div>

            <div class="mb-4">
                <label for="time_zone" class="form-label">Time Zone:</label>
                <select id="time_zone" name="time_zone" class="form-select" required>
                    <option value="Eastern Standard Time (GMT-5)" {% if profile.time_zone == 'Eastern Standard Time (GMT-5)' %}selected{% endif %}>Eastern Standard Time (GMT-5)</option>
                    <option value="Central Standard Time (GMT-6)" {% if profile.time_zone == 'Central Standard Time (GMT-6)' %}selected{% endif %}>Central Standard Time (GMT-6)</option>
                    <option value="Mountain Standard Time (GMT-7)" {% if profile.time_zone == 'Mountain Standard Time (GMT-7)' %}selected{% endif %}>Mountain Standard Time (GMT-7)</option>
                    <option value="Pacific Standard Time (GMT-8)" {% if profile.time_zone == 'Pacific Standard Time (GMT-8)' %}selected{% endif %}>Pacific Standard Time (GMT-8)</option>
                    <option value="Alaska Standard Time (GMT-9)" {% if profile.time_zone == 'Alaska Standard Time (GMT-9)' %}selected{% endif %}>Alaska Standard Time (GMT-9)</option>
                    <option value="Hawaii-Aleutian Standard Time (GMT-10)" {% if profile.time_zone == 'Hawaii-Aleutian Standard Time (GMT-10)' %}selected{% endif %}>Hawaii-Aleutian Standard Time (GMT-10)</option>
                </select>
            </div>

            <div class="mb-4">
                <label for="preferred_subject" class="form-label">Preferred Subject:</label>
                <input type="text" id="preferred_subject" name="preferred_subject" value="{{ profile.preferred_subject }}" class="form-control" required aria-required="true">
            </div>

            <div class="mb-4">
                <label for="cultural_background" class="form-label">Cultural Background:</label>
                <input type="text" id="cultural_background" name="cultural_background" value="{{ profile.cultural_background }}" class="form-control" required aria-required="true">
            </div>

            <!-- Insert Goals, Strengths, and Weaknesses sections here (already handled well) -->
            <fieldset class="mb-4">
                <legend class="col-form-label pt-0">Goals:</legend>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal1" name="goals" value="achieve career advancement" {% if 'achieve career advancement' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal1">Achieve career advancement</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal2" name="goals" value="improve skills" {% if 'improve skills' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal2">Improve skills</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal3" name="goals" value="learn new technologies" {% if 'learn new technologies' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal3">Learn new technologies</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal4" name="goals" value="prepare for certification exams" {% if 'prepare for certification exams' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal4">Prepare for certification exams</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal5" name="goals" value="transition to a new career" {% if 'transition to a new career' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal5">Transition to a new career</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal6" name="goals" value="enhance problem-solving abilities" {% if 'enhance problem-solving abilities' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal6">Enhance problem-solving abilities</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal7" name="goals" value="boost creativity and innovation" {% if 'boost creativity and innovation' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal7">Boost creativity and innovation</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal8" name="goals" value="develop leadership skills" {% if 'develop leadership skills' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal8">Develop leadership skills</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal9" name="goals" value="increase productivity and efficiency" {% if 'increase productivity and efficiency' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal9">Increase productivity and efficiency</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="goal10" name="goals" value="stay updated with industry trends" {% if 'stay updated with industry trends' in profile.goals %}checked{% endif %}>
                    <label class="form-check-label" for="goal10">Stay updated with industry trends</label>
                </div>
            </fieldset>

            <fieldset class="mb-4">
                <legend class="col-form-label pt-0">Strengths:</legend>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength1" name="strengths" value="creativity" {% if 'creativity' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength1">Creativity</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength2" name="strengths" value="imagination" {% if 'imagination' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength2">Imagination</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength3" name="strengths" value="teamwork" {% if 'teamwork' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength3">Teamwork</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength4" name="strengths" value="listening" {% if 'listening' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength4">Listening</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength5" name="strengths" value="critical thinking" {% if 'critical thinking' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength5">Critical thinking</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength6" name="strengths" value="patience" {% if 'patience' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength6">Patience</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength7" name="strengths" value="physical strength" {% if 'physical strength' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength7">Physical strength</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength8" name="strengths" value="practical skills" {% if 'practical skills' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength8">Practical skills</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength9" name="strengths" value="attention to detail" {% if 'attention to detail' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength9">Attention to detail</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength10" name="strengths" value="wisdom" {% if 'wisdeom' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength10">Wisdom</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength11" name="strengths" value="experience" {% if 'experience' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength11">Experience</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength12" name="strengths" value="leadership" {% if 'leadership' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength12">Leadership</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength13" name="strengths" value="decision-making" {% if 'decision-making' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength13">Decision-making</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength14" name="strengths" value="resilience" {% if 'resilience' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength14">Resilience</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength15" name="strengths" value="problem-solving" {% if 'problem-solving' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength15">Problem-solving</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength16" name="strengths" value="adaptability" {% if 'adaptability' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength16">Adaptability</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength17" name="strengths" value="analytical thinking" {% if 'analytical thinking' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength17">Analytical thinking</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength18" name="strengths" value="memory" {% if 'memory' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength18">Memory</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength19" name="strengths" value="empathy" {% if 'empathy' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength19">Empathy</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength20" name="strengths" value="strategic thinking" {% if 'strategic thinking' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength20">Strategic thinking</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength21" name="strengths" value="storytelling" {% if 'storytelling' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength21">Storytelling</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength22" name="strengths" value="observational skills" {% if 'observational skills' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength22">Observational skills</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength23" name="strengths" value="curiosity" {% if 'curiosity' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength23">Curiosity</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength24" name="strengths" value="innovative" {% if 'innovative' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength24">Innovative</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength25" name="strengths" value="cross-cultural communication" {% if 'cross-cultural communication' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength25">Cross-cultural communication</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="strength26" name="strengths" value="determination" {% if 'determination' in profile.strengths %}checked{% endif %}>
                    <label class="form-check-label" for="strength26">Determination</label>
                </div>
            </fieldset>

            <fieldset class="mb-4">
                <legend class="col-form-label pt-0">Weaknesses:</legend>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness1" name="weaknesses" value="delegation challenges" {% if 'delegation challenges' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness1">Delegation challenges</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness2" name="weaknesses" value="difficulty delegating" {% if 'difficulty delegating' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness2">Difficulty delegating</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness3" name="weaknesses" value="impatience" {% if 'impatience' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness3">Impatience</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness4" name="weaknesses" value="impulsiveness" {% if 'impulsiveness' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness4">Impulsiveness</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness5" name="weaknesses" value="introversion" {% if 'introversion' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness5">Introversion</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness6" name="weaknesses" value="micromanagement" {% if 'micromanagement' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness6">Micromanagement</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness7" name="weaknesses" value="mobility" {% if 'mobility' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness7">Mobility</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness8" name="weaknesses" value="overcommitment" {% if 'overcommitment' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness8">Overcommitment</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness9" name="weaknesses" value="overly critical of sources" {% if 'overly critical of sources' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness9">Overly critical of sources</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness10" name="weaknesses" value="overthinking" {% if 'overthinking' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness10">Overthinking</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness11" name="weaknesses" value="perfectionism" {% if 'perfectionism' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness11">Perfectionism</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness12" name="weaknesses" value="procrastination" {% if 'procrastination' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness12">Procrastination</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness13" name="weaknesses" value="public speaking" {% if 'public speaking' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness13">Public Speaking</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness14" name="weaknesses" value="self-doubt" {% if 'self-doubt' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness14">Self-doubt</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness15" name="weaknesses" value="short attention span" {% if 'short attention span' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness15">Short attention span</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness16" name="weaknesses" value="shyness" {% if 'shyness' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness16">Shyness</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness17" name="weaknesses" value="technology skills" {% if 'technology skills' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness17">Technology skills</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness18" name="weaknesses" value="tendency to overthink" {% if 'tendency to overthink' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness18">Tendency to overthink</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="weakness19" name="weaknesses" value="time management" {% if 'time management' in profile.weaknesses %}checked{% endif %}>
                    <label class="form-check-label" for="weakness19">Time management</label>
                </div>
            </fieldset>

            <fieldset class="mb-4">
                <legend class="col-form-label pt-0">Answer Type Weights:</legend>
            
                <div class="mb-2">
                    <label for="informational" class="form-label">Informational</label>
                    <input type="number" step="0.1" min="0" max="1" class="form-control" id="informational" name="answer_type_weights[informational]" value="{{ profile.answer_type_weights.informational }}">
                </div>
            
                <div class="mb-2">
                    <label for="real_world" class="form-label">Real World</label>
                    <input type="number" step="0.1" min="0" max="1" class="form-control" id="real_world" name="answer_type_weights[real_world]" value="{{ profile.answer_type_weights.real_world }}">
                </div>
            
                <div class="mb-2">
                    <label for="cause_and_effect" class="form-label">Cause and Effect</label>
                    <input type="number" step="0.1" min="0" max="1" class="form-control" id="cause_and_effect" name="answer_type_weights[cause_and_effect]" value="{{ profile.answer_type_weights.cause_and_effect }}">
                </div>
            
                <div class="mb-2">
                    <label for="goal_based" class="form-label">Goal Based</label>
                    <input type="number" step="0.1" min="0" max="1" class="form-control" id="goal_based" name="answer_type_weights[goal_based]" value="{{ profile.answer_type_weights.goal_based }}">
                </div>
            </fieldset>
            

            <!-- Submit Button -->
            <button type="submit" class="btn btn-primary w-100">Update Preferences</button>
        </form>
    </div>
</div>
{% endblock %}