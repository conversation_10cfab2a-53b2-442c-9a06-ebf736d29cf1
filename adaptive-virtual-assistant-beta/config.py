# config.py

import os
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

KEYWORDS = ['equation', 'variable', 'solve', 'simplify', 'factor', 'expression']

class Config:
    # Please use your own api key, this key will be deleted after June 30, 2025.
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'aSyAyl2yCFgeu5sIYegbe75iYs26l88aM0qI'
    GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')  # Must be set in .env
    KEYWORDS = ['equation', 'variable', 'solve', 'simplify', 'factor', 'expression']
