# utils/nlp_utils.py

import nltk
from nltk.corpus import wordnet as wn
from nltk.tokenize import word_tokenize
import string

# Ensure required NLTK data is downloaded
nltk.download('wordnet', quiet=True)
nltk.download('punkt', quiet=True)
nltk.download('stopwords', quiet=True)
from nltk.corpus import stopwords


# --- Function: preprocess_input ---
def preprocess_input(user_input):
    user_input = user_input.translate(str.maketrans('', '', string.punctuation))
    tokens = word_tokenize(user_input.lower())
    stop_words = set(stopwords.words('english'))
    tokens = [word for word in tokens if word not in stop_words]
    return tokens


# --- Function: get_synonyms ---
def get_synonyms(word):
    synonyms = set()
    for synset in wn.synsets(word):
        for lemma in synset.lemmas():
            synonyms.add(lemma.name())
    return synonyms


# --- Function: match_keywords ---
def match_keywords(user_tokens, keyword_synonyms):
    matched_keywords = set()
    for token in user_tokens:
        for keyword, synonyms in keyword_synonyms.items():
            if token in synonyms:
                matched_keywords.add(keyword)
    return matched_keywords



# --- Function: build_keyword_synonym_dict ---
def build_keyword_synonym_dict(keywords):
    """Enhanced synonym generation using WordNet"""
    synonym_dict = {}
    for keyword in keywords:
        synonyms = set()
        for synset in wn.synsets(keyword):
            for lemma in synset.lemmas():
                if lemma.name() != keyword:
                    synonyms.add(lemma.name().replace('_', ' '))
        synonym_dict[keyword] = [keyword] + list(synonyms)[:3]  # Limit to top 3
    return synonym_dict


# --- Function: process_question ---
def process_question(question, keyword_synonyms):
    """
    Process the question and return a list of keywords found based on the synonym dictionary.
    
    Args:
        question (str): The input question.
        keyword_synonyms (dict): A dictionary mapping keywords to lists of synonyms.
        
    Returns:
        list: A list of keywords that were found in the question.
    """
    matched_keywords = []
    question_lower = question.lower()
    for keyword, synonyms in keyword_synonyms.items():
        for synonym in synonyms:
            if synonym.lower() in question_lower:
                matched_keywords.append(keyword)
                break
    return matched_keywords