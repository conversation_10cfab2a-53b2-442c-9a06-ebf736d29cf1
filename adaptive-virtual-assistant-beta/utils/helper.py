# utils/helper.py

import re


# --- Function: sanitize_input ---
def sanitize_input(input_text):
    """
    Sanitize user input to prevent injection attacks.

    Args:
        input_text (str): The input text to sanitize.

    Returns:
        str: The sanitized text.
    """
    # Remove any unwanted characters
    sanitized = re.sub(r'[<>]', '', input_text)
    # Optionally, add more sanitization rules as needed
    return sanitized
