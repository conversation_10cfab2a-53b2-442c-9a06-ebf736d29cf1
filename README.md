
# Adaptive Virtual Assistant — Final Release v2.0 🚀

Welcome to the **Adaptive Virtual Assistant**, a full-stack Flask-based web application that leverages AI to deliver personalized educational support to students. This is the **final and complete release (v2.0)**, designed with modularity, readability, and extensibility in mind so that any new developer can pick it up without additional guidance.

---

## 📌 What This Project Does

This project uses Google’s **Gemini Generative AI API** to provide accurate and engaging answers to academic questions. Unlike generic assistants, **this application adapts responses based on the learner's profile**—such as their age, academic level, learning style, and personal preferences.

Components: a user dashboard, authentication system, admin panel, gamification (XP, badges, streaks), AI scoring for comprehension, and responsive frontend views.

---

## ⚠️ API Key Usage Notice

This repository contains a **demo Gemini API key** used for testing. This key will be **deleted after June 30, 2025**.

### 🔐 Files That Reference the API Key:
- `config.py` – Loads the Gemini API key from environment variables.
- `GeminiAPITest.py` – Uses a hardcoded Gemini key for testing output.

👉 **Please use your own API key by placing it in a `.env` file** (not tracked by Git). The format is:

```env
GEMINI_API_KEY=your-api-key-here
SECRET_KEY=your-flask-secret
```

---

## 🧠 Key Features

| Feature | Description |
|--------|-------------|
| AI-Powered Answers | Get accurate responses using the Gemini API |
| Personalized Profiles | Tailor answers to academic level, learning style, and interests |
| Gamification | Earn XP, level up, and collect badges |
| Adaptive Feedback | Users can test their understanding and receive comprehension scores |
| Admin Tools | View and manage user profiles, XP, and learning stats |
| Searchable History | Search, paginate, and clear Q&A history |
| Dark Mode & Accessibility | Fully responsive with dark mode and accessible inputs |
| Full Logging & Error Handling | All routes and actions are logged for security and debugging |

---

## 🏗️ Project Structure & File Breakdown

```
adaptive-virtual-assistant-beta/
├── app.py                   # Main Flask app entry point and setup
├── config.py                # App-wide configuration including keys and keyword list
├── requirements.txt         # Python dependency list
├── .env                     # Your secret API key and config (not included)
├── generate_profiles.py     # Script to generate hashed user profiles for testing
├── GeminiAPITest.py         # Standalone test of Gemini API response
│
├── data/
│   └── data.py              # Loads profiles.json into memory
│
├── models/
│   └── user.py              # User object with Flask-Login compatibility
│
├── routes/
│   ├── __init__.py          # Initializes route modules
│   ├── auth.py              # Handles login, logout, register
│   ├── main.py              # Main app pages (home, profile, leaderboard)
│   └── admin.py             # Admin dashboard, view/edit user stats
│
├── services/
│   ├── answer_service.py        # Handles calls to Gemini API
│   ├── similarity_service.py    # Scores answers for comprehension and adjusts weight
│   ├── auto_tagging.py          # Tags questions based on NLP keyword matching
│
├── utils/
│   ├── helper.py            # HTML sanitization and text utilities
│   └── nlp_utils.py         # WordNet-based synonym expansion and preprocessing
│
├── templates/               # All Jinja2 templates
│   ├── base.html            # Global layout
│   ├── index.html           # Ask a question
│   ├── profile.html         # View user details
│   ├── history.html         # Question history, searchable
│   ├── leaderboard.html     # XP-based leaderboard
│   ├── admin_dashboard.html # Admin view of all users
│   ├── login.html, register.html
│   ├── update_preferences.html
│   └── errors/*.html        # 403, 404, 500 error pages
│
├── static/                  # CSS, images, icons
│   ├── css/styles.css
│   └── images/
│       └── team_member_*.jpg
└── profiles.json            # User profiles stored in JSON
```

---

## ⚙️ Setup Guide

### 🔧 Requirements

- Python 3.7+
- Pip + Virtual Environment (venv recommended)
- Internet access (for Gemini API and NLTK downloads)

### 💻 Installation

```bash
git clone https://github.com/your-username/adaptive-virtual-assistant-beta.git
cd adaptive-virtual-assistant-beta
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 🔐 Configure Environment

Create a `.env` file in the root directory:

```
GEMINI_API_KEY=your-own-api-key
SECRET_KEY=your-own-flask-secret
```

---

## 🚀 Running the App

```bash
python app.py
```

Then visit: [http://localhost:5000](http://localhost:5000)

You’ll be greeted by a user-friendly homepage with predefined and custom question options.

---

## 🧪 Developer Tips

### Add New Users
Edit and run `generate_profiles.py` to add new users to `profiles.json`. Passwords are securely hashed.

### Extend AI Behavior
- Add logic in `services/answer_service.py`
- Adjust scoring in `similarity_service.py`
- Add new tags or keywords in `config.py`

### Customize User Experience
- Modify templates in `/templates`
- Use `update_preferences.html` to define new profile options
- Use weights to adapt responses by style (informational, real-world, etc.)

---

## 🧰 Technologies Used

| Technology | Purpose |
|------------|---------|
| Flask      | Core web framework |
| Flask-Login| User sessions and access control |
| WTForms    | Form validation and rendering |
| Bootstrap  | UI layout and responsiveness |
| Bleach     | Prevent HTML injection |
| Markdown   | Format AI responses |
| Google Gemini API | Generate AI-based answers |
| NLTK       | Tokenization, synonym matching |
| JSON       | Profile storage and Q&A history |

---

## 🚨 Final Notes

- This is version **2.0**, the final and stable release.
- The current Gemini API key (used in test/demo) will be **revoked after June 30, 2025**.
- **Do not commit sensitive credentials. Use `.env`.**

---

## 📄 License

This project is licensed under the MIT License. Use it freely and contribute back improvements.
